import { NextRequest, NextResponse } from 'next/server';

// Valid domains that can be proxied
const VALID_DOMAINS = ['vidsrc.xyz', 'vidsrc.in', 'vidsrc.pm', 'vidsrc.net'];

/**
 * Catch-all proxy route for VidSrc resources (CSS, JS, images, etc.)
 * 
 * This handles requests like:
 * /api/proxy/vidsrc/style.css?t=1710289820
 * /api/proxy/vidsrc/sources.js?t=1745104089
 * etc.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { searchParams } = new URL(request.url);
    const resolvedParams = await params;
    const pathSegments = resolvedParams?.path || [];
    const resourcePath = '/' + pathSegments.join('/');
    const queryString = searchParams.toString();
    const fullPath = queryString ? `${resourcePath}?${queryString}` : resourcePath;
    
    // Try each VidSrc domain until we find one that works
    for (const domain of VALID_DOMAINS) {
      const vidsrcResourceUrl = `https://${domain}${fullPath}`;
      
      console.log(`Attempting to fetch VidSrc resource from ${domain}: ${vidsrcResourceUrl}`);
      
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
        
        const response = await fetch(vidsrcResourceUrl, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'en-US,en;q=0.5',
            'Referer': `https://${domain}/`,
            'Origin': `https://${domain}`,
            'Sec-Fetch-Dest': 'script',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
          },
          signal: controller.signal,
          cache: 'no-store',
        }).finally(() => clearTimeout(timeoutId));
        
        if (response.ok) {
          const content = await response.arrayBuffer();
          const contentType = response.headers.get('Content-Type') || getContentTypeFromPath(resourcePath);
          
          console.log(`Successfully fetched VidSrc resource from ${domain}: ${resourcePath}`);
          
          return new NextResponse(content, {
            status: 200,
            headers: {
              'Content-Type': contentType,
              'Cache-Control': 'public, max-age=3600',
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
              'X-Proxied-From': domain,
            }
          });
        } else {
          console.log(`Resource not found on ${domain}: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.log(`Failed to fetch from ${domain}:`, error instanceof Error ? error.message : 'Unknown error');
        continue; // Try next domain
      }
    }
    
    // If we get here, none of the domains worked
    console.log(`Resource not found on any VidSrc domain: ${resourcePath}`);
    return NextResponse.json(
      { error: 'Resource not found on any VidSrc domain', path: resourcePath },
      { status: 404 }
    );
    
  } catch (error) {
    console.error('VidSrc resource proxy error:', error);
    return NextResponse.json(
      { error: 'Proxy error', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

/**
 * Determine content type based on file extension
 */
function getContentTypeFromPath(path: string): string {
  const extension = path.split('.').pop()?.toLowerCase();
  
  switch (extension) {
    case 'js':
      return 'application/javascript';
    case 'css':
      return 'text/css';
    case 'json':
      return 'application/json';
    case 'png':
      return 'image/png';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'gif':
      return 'image/gif';
    case 'svg':
      return 'image/svg+xml';
    case 'woff':
      return 'font/woff';
    case 'woff2':
      return 'font/woff2';
    case 'ttf':
      return 'font/ttf';
    case 'eot':
      return 'application/vnd.ms-fontobject';
    default:
      return 'application/octet-stream';
  }
}

// Handle OPTIONS requests for CORS
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
