'use client'

import React, { useEffect, useRef, useState, useCallback, useMemo } from 'react'
import { useSearchParams } from 'next/navigation'
import { cn } from '@/lib/utils'
import { getBestDomain, getNextBestDomain } from '@/lib/domain-health'

/**
 * Props for the VidSrcPlayer component
 *
 * @interface VidSrcPlayerProps
 * @property {string} [imdbId] - The IMDb ID for the content (format: 'tt1234567')
 * @property {string} [tmdbId] - The TMDb ID for the content
 * @property {'movie' | 'show' | 'tv'} [type=movie] - The type of content to play
 * @property {number} [season=1] - The season number for TV shows
 * @property {number} [episode=1] - The episode number for TV shows
 * @property {string} [className] - Additional CSS classes to apply to the player container
 * @property {string} [height=100%] - The height of the player container
 * @property {boolean} [shouldRefresh=false] - Whether to force a refresh of the player (useful for episode changes)
 * @property {boolean} [autoNext=false] - Whether to enable auto-next functionality for TV shows
 * @property {(season: number, episode: number) => void} [onAutoNext] - Callback when auto-next occurs
 */
interface VidSrcPlayerProps {
  imdbId?: string
  tmdbId?: string
  type?: 'movie' | 'show' | 'tv'
  season?: number
  episode?: number
  className?: string
  height?: string
  shouldRefresh?: boolean  // New prop to control when player should refresh
  autoNext?: boolean  // New prop to enable auto-next functionality for TV shows
  onAutoNext?: (season: number, episode: number) => void // New callback for auto-next
}

/**
 * VidSrc Player Component
 *
 * Renders a video player using the VidSrc API with automatic domain fallback
 * and error handling. Supports both movies and TV shows.
 *
 * Features:
 * - Supports both IMDb and TMDb IDs for content
 * - Handles TV shows with season and episode selection
 * - Provides loading states and error handling
 * - Supports player refreshing when episodes change
 * - Auto-retries on failures
 *
 * @param {VidSrcPlayerProps} props - The component props
 * @returns {React.ReactElement} The rendered video player
 */
export default function VidSrcPlayer({
  imdbId,
  tmdbId,
  type = 'movie',
  season = 1,
  episode = 1,
  className = '',
  height = '100%',
  shouldRefresh = false, // Controls explicit refresh requests
  autoNext = false, // Controls auto-next functionality for TV shows
  onAutoNext, // Callback for auto-next event
}: VidSrcPlayerProps) {
  console.log('[VidSrcPlayer] ===== COMPONENT MOUNTING =====');
  console.log('[VidSrcPlayer] Mount props:', { imdbId, tmdbId, type, season, episode });

  const searchParams = useSearchParams();
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [domain, setDomain] = useState('vidsrc.xyz');
  const [useProxy, setUseProxy] = useState(true); // Always start with proxy

  // Component unmount logging
  useEffect(() => {
    return () => {
      console.log('[VidSrcPlayer] ===== COMPONENT UNMOUNTING =====');
    };
  }, []);

  // For tracking domain attempts during initial load
  const domainsTriedRef = useRef<string[]>([]);

  // Track consecutive provider switches
  const providerSwitchCount = useRef<number>(0);

  // Enhanced mobile device detection with iOS-specific detection
  const isMobile = useCallback(() => {
    if (typeof window !== "undefined") {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      );
    }
    return false;
  }, []);

  // iOS-specific detection
  const isIOS = useCallback(() => {
    if (typeof window !== "undefined") {
      // Modern way to detect iOS without using deprecated platform property
      return /iPhone|iPad|iPod/i.test(navigator.userAgent) ||
             (/Mac/i.test(navigator.userAgent) && navigator.maxTouchPoints > 0);
    }
    return false;
  }, []);

  // VidSrc domains based on official documentation - prioritize most reliable ones first
  const vidsrcDomains = useMemo(() => ['vidsrc.xyz', 'vidsrc.in', 'vidsrc.pm', 'vidsrc.net'], []);

  // Available provider options - updated with official domains only
  const providerOptions = useMemo(() => [
    { id: 'vidsrc.xyz', name: 'VidSrc (xyz)' },
    { id: 'vidsrc.in', name: 'VidSrc (in)' },
    { id: 'vidsrc.pm', name: 'VidSrc (pm)' },
    { id: 'vidsrc.net', name: 'VidSrc (net)' }
  ], []);

  // Utility function to determine the best provider based on content ID
  const getBestProvider = useCallback(async (): Promise<string> => {
    try {
      // Use domain health check to find best domain
      const bestDomain = await getBestDomain();
      console.log(`[VidSrcPlayer] Selected optimal domain from health check: ${bestDomain}`);
      return bestDomain;
    } catch (err) {
      console.error('[VidSrcPlayer] Error finding best domain:', err);
      return 'vidsrc.xyz'; // Default fallback to official domain
    }
  }, []);

  // Initialize with the best provider based on health checks
  useEffect(() => {
    const initDomain = async () => {
      const bestProvider = await getBestProvider();
      console.log(`[VidSrcPlayer] Selected initial provider: ${bestProvider}`);
      setDomain(bestProvider);
    };

    initDomain();
  }, [getBestProvider]);

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retry, setRetry] = useState(0);
  const refreshCounter = useRef(0);
  const [playerUrl, setPlayerUrl] = useState('');
  const [showProviderOptions, setShowProviderOptions] = useState(false);

  // Get URL parameters for season and episode if available
  const urlSeason = searchParams?.get('season');
  const urlEpisode = searchParams?.get('episode');

  // Use URL params or default props values, ensure they're numbers
  const actualSeason = urlSeason ? parseInt(urlSeason) : season;
  const actualEpisode = urlEpisode ? parseInt(urlEpisode) : episode;

  // Consolidate the actual content type for consistent usage
  const contentType = type === 'show' || type === 'tv' ? 'tv' : 'movie';

  // Log parameters on component mount and whenever they change
  useEffect(() => {
    console.log('[VidSrcPlayer] Player config:', {
      tmdbId,
      imdbId,
      type: contentType,
      season: actualSeason,
      episode: actualEpisode,
      domain
    });
  }, [tmdbId, imdbId, contentType, actualSeason, actualEpisode, domain]);

  // If current domain/provider fails, try switching to next provider
  const handlePlayerError = useCallback(async () => {
    // Increment provider switch count
    providerSwitchCount.current += 1;

    // Calculate delay based on device and number of switches
    // Use longer delays for iOS devices
    const baseDelay = isIOS() ? 5000 : (isMobile() ? 3000 : 1000);
    const exponentialDelay = Math.min(baseDelay * Math.pow(1.5, providerSwitchCount.current - 1), 15000);

    console.log(`[VidSrcPlayer] Provider switch #${providerSwitchCount.current}, applying delay of ${exponentialDelay}ms`);
    console.log(`[VidSrcPlayer] Device detection: Mobile: ${isMobile()}, iOS: ${isIOS()}`);

    // Apply delay to prevent rapid provider switching
    await new Promise(resolve => setTimeout(resolve, exponentialDelay));

    // If we're not using the proxy yet, try using it with the current domain first
    if (!useProxy) {
      console.log(`[VidSrcPlayer] Direct embed failed, trying proxy with ${domain}`);
      setUseProxy(true);
      setLoading(true);
      setRetry(0);
      refreshCounter.current++;
      return;
    }

    try {
      // Get next best domain based on health check
      const nextDomain = await getNextBestDomain(domain);
      console.log(`[VidSrcPlayer] Current provider (${domain}) failed with proxy, trying ${nextDomain}`);
      setDomain(nextDomain);
      setUseProxy(false); // Try direct embed first with new domain
      setLoading(true);
      setRetry(0);
      refreshCounter.current++;
    } catch (err) {
      // Fallback to simple rotation if health check fails
      const currentIndex = providerOptions.findIndex(p => p.id === domain);
      const nextIndex = (currentIndex + 1) % providerOptions.length;
      const nextProvider = providerOptions[nextIndex];

      console.log(`[VidSrcPlayer] Falling back to next provider: ${nextProvider.id}`);
      setDomain(nextProvider.id);
      setUseProxy(false);
      setLoading(true);
      setRetry(0);
      refreshCounter.current++;
    }
  }, [domain, providerOptions, useProxy, isMobile, isIOS]);

  // Handle switching to a specific provider
  const switchToProvider = useCallback((providerId: string) => {
    console.log(`[VidSrcPlayer] Manually switching to provider: ${providerId}`);
    setDomain(providerId);
    setShowProviderOptions(false);
    setLoading(true);
    setRetry(0);
    refreshCounter.current++;
  }, []);

  // Render provider selection UI
  const renderProviderOptions = () => (
    <div className="absolute top-0 left-0 right-0 p-4 bg-black/80 z-10 rounded-lg">
      <h3 className="text-white text-sm font-medium mb-3">Try a different provider:</h3>
      <div className="flex flex-wrap gap-2">
        {providerOptions.map(provider => (
          <button
            key={provider.id}
            onClick={() => switchToProvider(provider.id)}
            className={`px-3 py-1.5 text-xs rounded-md ${
              domain === provider.id
                ? 'bg-vista-blue text-white'
                : 'bg-gray-700 text-gray-200 hover:bg-gray-600'
            }`}
          >
            {provider.name}
          </button>
        ))}
      </div>
      <div className="mt-2 flex items-center">
        <label className="inline-flex items-center cursor-pointer">
          <input
            type="checkbox"
            className="sr-only peer"
            checked={useProxy}
            onChange={() => {
              setUseProxy(!useProxy);
              setLoading(true);
              refreshCounter.current++;
            }}
          />
          <div className="relative w-10 h-5 bg-gray-700 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:start-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-vista-blue"></div>
          <span className="ms-3 text-xs text-gray-300">Use Proxy</span>
        </label>
      </div>
    </div>
  );

  // Helper function to generate the player URL
  const generatePlayerUrl = useCallback(() => {
    console.log('[VidSrcPlayer] ===== GENERATE PLAYER URL CALLED =====');
    console.log('[VidSrcPlayer] Available IDs:', { imdbId, tmdbId });

    if (!imdbId && !tmdbId) {
      console.error('[VidSrcPlayer] CANNOT GENERATE URL: No IMDB or TMDB ID available');
      return '';
    }

    // Normalize content type (accept 'tv' as well as 'show')
    // First ensure type is defined to prevent 'cannot access uninitialized variable' error
    const typeValue = type || 'movie'; // Default to 'movie' if type is undefined
    const normalizedType = typeValue.toLowerCase() === 'show' || typeValue.toLowerCase() === 'tv'
      ? 'tv'
      : 'movie';

    // Log the URL generation details for debugging
    console.log('[VidSrcPlayer] Generating URL with:', {
      imdbId,
      tmdbId,
      normalizedType,
      originalType: type,
      season: actualSeason,
      episode: actualEpisode,
      domain,
      useProxy
    });

    // Additional debug for content type normalization
    console.log(`[VidSrcPlayer] Content type normalization: "${typeValue}" -> "${normalizedType}"`);
    if (typeValue.toLowerCase() !== 'movie' && typeValue.toLowerCase() !== 'tv' && typeValue.toLowerCase() !== 'show') {
      console.warn(`[VidSrcPlayer] Unexpected content type: "${typeValue}". This might cause playback issues.`);
    }

    // Direct URL to VidSrc API
    let directUrl = `https://${domain}/embed/${normalizedType}?`;

    // Add the appropriate ID (prefer IMDB, fallback to TMDB)
    if (imdbId) {
      directUrl += `imdb=${imdbId}`;
    } else if (tmdbId) {
      directUrl += `tmdb=${tmdbId}`;
    }

    // Add season and episode for TV shows
    if (normalizedType === 'tv' && actualSeason) {
      directUrl += `&season=${actualSeason}`;

      // Only add episode if season is provided
      if (actualEpisode) {
        directUrl += `&episode=${actualEpisode}`;
      }

      // Add autonext parameter for TV shows if enabled
      if (autoNext) {
        directUrl += `&autonext=1`;
      }
    }

    // Track this domain attempt for initial load
    if (!domainsTriedRef.current.includes(domain)) {
      domainsTriedRef.current.push(domain);
    }

    // Always use proxy initially for better first-load experience
    if (useProxy || refreshCounter.current <= 1) {
      const encodedUrl = encodeURIComponent(directUrl);
      const proxyUrl = `/api/proxy/vidsrc?url=${encodedUrl}`;
      console.log('[VidSrcPlayer] ===== USING PROXY URL =====');
      console.log('[VidSrcPlayer] Direct URL:', directUrl);
      console.log('[VidSrcPlayer] Encoded URL:', encodedUrl);
      console.log('[VidSrcPlayer] Final Proxy URL:', proxyUrl);
      return proxyUrl;
    }

    console.log('[VidSrcPlayer] Using direct URL:', directUrl);
    return directUrl;
  }, [imdbId, tmdbId, type, actualSeason, actualEpisode, domain, useProxy, autoNext]);

  // Debug Props with more detailed logging
  console.log('[VidSrcPlayer] COMPONENT MOUNTED with props:', {
    imdbId,
    tmdbId,
    type,
    season,
    episode,
    actualSeason,
    actualEpisode,
    className,
    shouldRefresh
  });

  // Critical debug: Check if we have required IDs
  if (!imdbId && !tmdbId) {
    console.error('[VidSrcPlayer] CRITICAL: No IMDB or TMDB ID provided! Player will not work.');
  } else {
    console.log('[VidSrcPlayer] IDs available:', { imdbId: !!imdbId, tmdbId: !!tmdbId });
  }

  // Additional debug for content type
  const safeType = type || 'movie'; // Default to 'movie' if type is undefined
  console.log(`[VidSrcPlayer] Content type received: "${safeType}", normalized to: "${safeType.toLowerCase() === 'show' || safeType.toLowerCase() === 'tv' ? 'tv' : 'movie'}"`);

  // Track previous values to detect actual changes
  const prevSeasonRef = useRef(actualSeason);
  const prevEpisodeRef = useRef(actualEpisode);

  // Update internal state when props change, but only refresh player if it's explicitly requested
  useEffect(() => {
    // Set the initial season/episode without refreshing player
    if (refreshCounter.current === 0) {
      console.log('VidSrcPlayer: Initial load, setting season/episode without refreshing');
      setLoading(true);
      setRetry(0);
      refreshCounter.current++;
      prevSeasonRef.current = actualSeason;
      prevEpisodeRef.current = actualEpisode;
      return;
    }

    // Check if season or episode actually changed from previous values
    const seasonChanged = actualSeason !== prevSeasonRef.current;
    const episodeChanged = actualEpisode !== prevEpisodeRef.current;
    const hasChanged = seasonChanged || episodeChanged;

    if (hasChanged) {
      console.log(`VidSrcPlayer: Episode/Season changed from S${prevSeasonRef.current}E${prevEpisodeRef.current} to S${actualSeason}E${actualEpisode}`);
      
      // Update previous values
      prevSeasonRef.current = actualSeason;
      prevEpisodeRef.current = actualEpisode;
      
      // Always refresh when episode/season changes, regardless of shouldRefresh flag
      console.log(`VidSrcPlayer: Refreshing player for new episode: S${actualSeason}E${actualEpisode}`);
      setLoading(true);
      setRetry(0);
      refreshCounter.current++;
    } else if (shouldRefresh) {
      // Force refresh even if no change detected (manual refresh request)
      console.log(`VidSrcPlayer: Manual refresh requested for S${actualSeason}E${actualEpisode}`);
      setLoading(true);
      setRetry(0);
      refreshCounter.current++;
    }
  }, [actualSeason, actualEpisode, shouldRefresh]);

  /**
   * Updates the player URL based on the content type and available IDs
   *
   * This function constructs the appropriate embed URL for the VidSrc player
   * based on whether it's a movie or TV show, and which content IDs are available.
   *
   * @returns {void}
   */
  const updatePlayer = useCallback(() => {
    console.log(`VidSrcPlayer: Updating player for ${type} with imdbId: ${imdbId}, tmdbId: ${tmdbId}`);
    console.log(`VidSrcPlayer: Current season: ${actualSeason}, episode: ${actualEpisode}`);

    // Clear previous errors when updating
    setError(null);

    // Immediately set loading state
    setLoading(true);

    // Construct the player URL based on the content type and available IDs
    const newUrl = generatePlayerUrl();

    // Only update the player URL if we have a valid URL and it's different from the current one
    if (newUrl && newUrl !== playerUrl) {
      console.log(`VidSrcPlayer: Setting new player URL: ${newUrl}`);
      setPlayerUrl(newUrl);
      // Allow a grace period before declaring success
      setTimeout(() => {
        if (loading) {
          console.log('VidSrcPlayer: Setting isLoading=false after grace period');
          setLoading(false);
        }
      }, 2000);
    } else if (!newUrl) {
      console.error('VidSrcPlayer: Could not create a valid player URL - missing required IDs');
      setLoading(false); // Set loading to false if we can't create a URL
      setError('Unable to create player URL - missing content information');
    } else {
      // URL is the same as before, no need to reload
      setLoading(false);
    }
  }, [imdbId, tmdbId, type, actualSeason, actualEpisode, playerUrl, loading, generatePlayerUrl]);

  // Update the player when any of the player props change
  useEffect(() => {
    // Before updating player, ensure we're on a valid domain
    if (refreshCounter.current === 0) {
      // Check if we're in a watch party as a joiner
      const isWatchParty = typeof window !== 'undefined' &&
                          window.location.href.includes('mode=party') &&
                          !window.location.href.includes('isHost=true');

      if (isWatchParty) {
        // For watch party joiners, skip health check and use proxy immediately
        // This speeds up initial loading significantly
        console.log('[VidSrcPlayer] Watch party joiner detected, using fast load path');
        setDomain('vidsrc.xyz'); // Use the most reliable domain
        setUseProxy(true);
        updatePlayer();
      } else {
        // For normal users and hosts, do the full health check
        getBestProvider().then(bestDomain => {
          setDomain(bestDomain);
          setUseProxy(true); // Always start with proxy for first load
          updatePlayer();
        }).catch(() => {
          // Fallback to first domain if health check fails
          setDomain(vidsrcDomains[0]);
          setUseProxy(true);
          updatePlayer();
        });
      }
    } else {
      updatePlayer();
    }
  }, [updatePlayer, getBestProvider, vidsrcDomains]);

  // Try all providers if current one fails
  useEffect(() => {
    // If we have an error and haven't exceeded retry limit, try with a different provider
    if (error && retry < providerOptions.length - 1) {
      const timer = setTimeout(() => {
        // Automatically try the next provider
        handlePlayerError();
      }, isMobile() ? 4000 : 2000); // Longer delay on mobile

      return () => clearTimeout(timer);
    }
  }, [error, retry, handlePlayerError, providerOptions.length, isMobile]);

  /**
   * Monitors for explicit episode change events
   *
   * Sets up an event listener for custom 'episodeChange' events that can be
   * triggered elsewhere in the application. When an episode change event
   * is received, the player will refresh with the new episode data.
   *
   * @returns {void}
   */
  // Listen for playback control messages from parent
  useEffect(() => {
    const handlePlaybackControl = (event: MessageEvent) => {
      if (!event.data || typeof event.data !== 'string') return;

      try {
        const data = JSON.parse(event.data);
        
        // Debug: Log ALL iframe messages when auto-next is enabled
        if (autoNext && type === 'tv') {
          console.log(`🔍 [VidSrcPlayer] ALL IFRAME MESSAGES (auto-next enabled):`, data);
          console.log(`🔍 [VidSrcPlayer] Message origin:`, event.origin);
          console.log(`🔍 [VidSrcPlayer] onAutoNext callback exists:`, !!onAutoNext);
        }

        // Handle play/pause commands
        if (data.action === 'play' || data.action === 'pause') {
          console.log(`[VidSrcPlayer] Received ${data.action} command`);

          // Send command to the iframe
          if (iframeRef.current && iframeRef.current.contentWindow) {
            // Send multiple times to ensure it gets through
            for (let i = 0; i < 3; i++) {
              setTimeout(() => {
                if (iframeRef.current && iframeRef.current.contentWindow) {
                  iframeRef.current.contentWindow.postMessage(JSON.stringify({
                    action: data.action
                  }), '*');
                }
              }, i * 100); // Send at 0ms, 100ms, and 200ms
            }

            // Also try to directly control the video element if possible
            try {
              const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;
              if (iframeDoc) {
                const videoElement = iframeDoc.querySelector('video');
                if (videoElement) {
                  if (data.action === 'play') {
                    videoElement.play();
                    // Confirm playback started
                    setTimeout(() => {
                      if (videoElement.paused) {
                        videoElement.play();
                      }
                    }, 300);
                  } else {
                    videoElement.pause();
                  }
                }
              }
            } catch (error) {
              // CORS will likely prevent this, which is fine
              console.log('[VidSrcPlayer] Could not directly control video element due to security restrictions');
            }
          }
        }

        // Handle seek commands
        if (data.action === 'seek' && data.time !== undefined) {
          console.log(`[VidSrcPlayer] Received seek command to ${data.time}`);

          if (iframeRef.current && iframeRef.current.contentWindow) {
            iframeRef.current.contentWindow.postMessage(JSON.stringify({
              action: 'seek',
              time: data.time
            }), '*');

            // Also try to directly control the video element
            try {
              const iframeDoc = iframeRef.current.contentDocument || iframeRef.current.contentWindow.document;
              if (iframeDoc) {
                const videoElement = iframeDoc.querySelector('video');
                if (videoElement) {
                  videoElement.currentTime = data.time;
                }
              }
            } catch (error) {
              // CORS will likely prevent this
              console.log('[VidSrcPlayer] Could not directly seek video due to security restrictions');
            }
          }
        }
        
        // Detect auto-next episode changes - Enhanced detection
        if (autoNext && type === 'tv' && onAutoNext) {
          console.log(`🔍 [VidSrcPlayer] Checking for auto-next patterns in message...`);
          
          // Method 1: Direct episode-change action
          if (data.action === 'episode-change') {
            const { season: newSeason, episode: newEpisode } = data;
            console.log(`🎬 [VidSrcPlayer] DIRECT AUTO-NEXT DETECTED: S${newSeason}E${newEpisode}`);
            onAutoNext(newSeason, newEpisode);
            return;
          }
          
          // Method 2: URL parameter changes
          if (data.url) {
            try {
              const url = new URL(data.url);
              const urlSeason = url.searchParams.get('season');
              const urlEpisode = url.searchParams.get('episode');
              
              if (urlSeason && urlEpisode) {
                const newSeason = parseInt(urlSeason, 10);
                const newEpisode = parseInt(urlEpisode, 10);
                
                // Only trigger if different from current values
                if (newSeason !== season || newEpisode !== episode) {
                  console.log(`🎬 [VidSrcPlayer] URL AUTO-NEXT DETECTED: S${newSeason}E${newEpisode}`);
                  onAutoNext(newSeason, newEpisode);
                  return;
                }
              }
            } catch (error) {
              console.error('[VidSrcPlayer] Error parsing URL for episode change:', error);
            }
          }
          
          // Method 3: Look for common auto-next indicators
          const possibleAutoNextIndicators = [
            'episodeChange', 'episode-change', 'nextEpisode', 'next-episode',
            'urlChange', 'url-change', 'navigation', 'pageChange'
          ];
          
          const hasAutoNextIndicator = possibleAutoNextIndicators.some(indicator => 
            data.type === indicator || 
            data.action === indicator || 
            data.event === indicator ||
            (typeof data === 'object' && Object.keys(data).some(key => key.includes(indicator)))
          );
          
          if (hasAutoNextIndicator) {
            console.log(`🎬 [VidSrcPlayer] POTENTIAL AUTO-NEXT DETECTED via indicator:`, data);
            
            // Try to extract episode info from various possible structures
            let newSeason = season;
            let newEpisode = episode;
            
            // Check various possible property names
            if (data.season || data.seasonNumber || data.s) newSeason = data.season || data.seasonNumber || data.s;
            if (data.episode || data.episodeNumber || data.e) newEpisode = data.episode || data.episodeNumber || data.e;
            
            // If we couldn't extract specific values, assume next episode
            if (newSeason === season && newEpisode === episode) {
              console.log(`🎬 [VidSrcPlayer] No specific episode info found, assuming next episode`);
              newEpisode = episode + 1;
            }
            
            console.log(`🎬 [VidSrcPlayer] CALLING onAutoNext with S${newSeason}E${newEpisode}`);
            onAutoNext(newSeason, newEpisode);
          }
        }
      } catch (error) {
        // Ignore parsing errors
      }
    };

    window.addEventListener('message', handlePlaybackControl);

    return () => {
      window.removeEventListener('message', handlePlaybackControl);
    };
  }, [iframeRef, type, season, episode, onAutoNext, autoNext]);

  // Additional auto-next detection via iframe URL monitoring
  useEffect(() => {
    if (!autoNext || type !== 'tv' || !onAutoNext || !iframeRef.current) return;

    console.log(`🔍 [VidSrcPlayer] Setting up iframe URL monitoring for auto-next detection`);

    let lastUrl = '';
    
    const checkIframeUrl = () => {
      try {
        if (iframeRef.current && iframeRef.current.contentWindow) {
          const currentUrl = iframeRef.current.contentWindow.location.href;
          
          if (currentUrl !== lastUrl) {
            console.log(`🔍 [VidSrcPlayer] Iframe URL changed:`, currentUrl);
            
            // Try to extract season/episode from URL
            const seasonMatch = currentUrl.match(/season=(\d+)/);
            const episodeMatch = currentUrl.match(/episode=(\d+)/);
            
            if (seasonMatch && episodeMatch) {
              const newSeason = parseInt(seasonMatch[1], 10);
              const newEpisode = parseInt(episodeMatch[1], 10);
              
              if (newSeason !== season || newEpisode !== episode) {
                console.log(`🎬 [VidSrcPlayer] IFRAME URL AUTO-NEXT DETECTED: S${newSeason}E${newEpisode}`);
                onAutoNext(newSeason, newEpisode);
              }
            }
            
            lastUrl = currentUrl;
          }
        }
      } catch (error) {
        // CORS will prevent this in most cases, but worth trying
        console.log(`🔍 [VidSrcPlayer] Cannot access iframe URL due to CORS:`, error instanceof Error ? error.message : String(error));
      }
    };

    // Check iframe URL every 2 seconds
    const urlCheckInterval = setInterval(checkIframeUrl, 2000);

    return () => {
      clearInterval(urlCheckInterval);
    };
  }, [autoNext, type, onAutoNext, season, episode, iframeRef]);

  /**
   * Monitors for explicit episode change events
   *
   * Sets up an event listener for custom 'episodeChange' events that can be
   * triggered elsewhere in the application. When an episode change event
   * is received, the player will refresh with the new episode data.
   *
   * @returns {void}
   */
  useEffect(() => {
    console.log('VidSrcPlayer: Setting up event listener for episodeChange');

    const handleEpisodeChange = (event: CustomEvent) => {
      if (event.detail && typeof event.detail === 'object') {
        const { season, episode } = event.detail as { season: number; episode: number };

        console.log(`VidSrcPlayer: Received episode change event - S${season}E${episode}`);

        // Check if the season/episode actually changed to avoid unnecessary updates
        if (season === actualSeason && episode === actualEpisode) {
          console.log('VidSrcPlayer: No change in season/episode, skipping refresh');
          return;
        }

        console.log(`VidSrcPlayer: Updating player for new episode: S${season}E${episode}`);

        // We can't directly set these since they're derived from props/URL
        // Instead, we'll force a refresh with the new URL parameters
        const newUrl = generatePlayerUrl();
        setPlayerUrl(newUrl);
        refreshCounter.current++;

        // Reset loading state
        setLoading(true);
        // Reset retry count
        setRetry(0);
      }
    };

    window.addEventListener('episodeChange', handleEpisodeChange as EventListener);

    return () => {
      window.removeEventListener('episodeChange', handleEpisodeChange as EventListener);
    };
  }, [actualSeason, actualEpisode, generatePlayerUrl]);

  // Add console logs for debugging
  console.log('VidSrcPlayer render:', {
    imdbId,
    tmdbId,
    type,
    actualSeason,
    actualEpisode,
    playerUrl,
    loading,
    retry,
    shouldRefresh,
    useProxy
  });

  // If we're still loading, show a loading message
  if (loading) {
    return (
      <div className={`flex items-center justify-center bg-black ${className}`} style={{ height }}>
        <div className="flex flex-col items-center gap-3">
          <div className="w-10 h-10 border-4 border-vista-blue/20 border-t-vista-blue rounded-full animate-spin"></div>
          <div className="text-white text-sm">Loading {type === 'movie' ? 'movie' : `S${actualSeason}E${actualEpisode}`}...</div>
        </div>
      </div>
    )
  }

  // If we don't have a player URL, show an error with retry button
  if (!playerUrl) {
    return (
      <div className={`flex flex-col items-center justify-center bg-black ${className}`} style={{ height }}>
        <div className="text-white mb-4">
          Unable to load player. Missing content ID.
        </div>
        <button
          className="px-4 py-2 bg-vista-blue text-white rounded-md"
          onClick={() => {
            setRetry(0);
            setLoading(true);
            updatePlayer();
          }}
        >
          Retry
        </button>
      </div>
    )
  }

  // Render the player
  return (
    <div
      ref={containerRef}
      className={cn(
        "relative overflow-hidden bg-black w-full rounded-lg flex justify-center items-center",
        className
      )}
      style={{
        height,
        aspectRatio: "16 / 9", // Maintain aspect ratio regardless of container size
        minHeight: "240px",  // Ensure minimum height
        maxWidth: "100%",    // Prevent overflow
      }}
    >
      {showProviderOptions && renderProviderOptions()}

      <div className="absolute top-2 right-2 z-10 flex gap-2">
        <button
          onClick={() => setShowProviderOptions(prev => !prev)}
          className="p-1.5 bg-black/60 hover:bg-black/80 rounded-full text-white"
          title="Change video provider"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
          </svg>
        </button>
        <button
          onClick={handlePlayerError}
          className="p-1.5 bg-black/60 hover:bg-black/80 rounded-full text-white"
          title="Try next provider"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
          </svg>
        </button>
      </div>

      <div
        className="w-full h-full overflow-hidden"
        style={{
          position: "relative",
          display: "block",
          aspectRatio: "16/9"
        }}
      >
        <iframe
          key={`${refreshCounter.current}-${actualSeason}-${actualEpisode}`}
          ref={iframeRef}
          src={playerUrl}
          className="absolute top-0 left-0 w-full h-full"
          style={{
            border: 'none',
            width: '100%',
            height: '100%',
            minHeight: '100%',
            maxHeight: '100%',
            overflow: 'hidden',
            aspectRatio: '16/9'
          }}
          allow="autoplay; encrypted-media; picture-in-picture; fullscreen; web-share"
          loading="lazy"
          title="VidSrc Player"
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox allow-presentation allow-orientation-lock allow-downloads allow-modals"
          referrerPolicy="no-referrer"
          onLoad={() => {
            console.log(`VidSrcPlayer: iframe loaded for ${type === 'movie' ? 'movie' : `S${actualSeason}E${actualEpisode}`}`);
            console.log(`VidSrcPlayer: Device detection - Mobile: ${isMobile()}, iOS: ${isIOS()}`);
            setLoading(false);

            // Ensure the iframe maintains its size after loading
            if (iframeRef.current) {
              const iframe = iframeRef.current;
              iframe.style.width = '100%';
              iframe.style.height = '100%';
              iframe.style.minHeight = '100%';
              iframe.style.overflow = 'hidden';

              // iOS-specific handling
              if (isIOS()) {
                console.log('VidSrcPlayer: Applying iOS-specific iframe fixes');
                // Try to force a repaint which can help with iOS rendering issues
                setTimeout(() => {
                  if (iframe) {
                    iframe.style.display = 'none';
                    // Force a reflow
                    void iframe.offsetHeight;
                    iframe.style.display = 'block';
                  }
                }, 500);
              }

              // Additional HitsTV specific fix for the aspect ratio
              const onMessageHandler = (event: MessageEvent) => {
                // Check if iframe has loaded HitsTV content
                if (iframe.contentWindow &&
                    (iframe.contentWindow.location.href.includes('hitstv') ||
                     event.origin.includes('hitstv'))) {

                  // Special handling for HitsTV
                  console.log("VidSrcPlayer: Detected HitsTV player, applying aspect ratio fix");

                  // Try to inject CSS to fix HitsTV aspect ratio
                  try {
                    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
                    if (iframeDoc) {
                      // Create a style element
                      const style = iframeDoc.createElement('style');
                      style.textContent = `
                        .video-js, .plyr--video, [id*="player"], [class*="player"] {
                          position: relative !important;
                          height: auto !important;
                          min-height: unset !important;
                          aspect-ratio: 16/9 !important;
                          width: 100% !important;
                        }
                        video {
                          object-fit: contain !important;
                          width: 100% !important;
                          height: auto !important;
                          aspect-ratio: 16/9 !important;
                        }
                      `;
                      iframeDoc.head.appendChild(style);
                    }
                  } catch (e) {
                    // CORS will likely prevent this
                  }
                }
              };

              // Listen for messages from iframe that might indicate it's ready
              window.addEventListener('message', onMessageHandler);

              // Remove listener after 10 seconds
              setTimeout(() => {
                window.removeEventListener('message', onMessageHandler);
              }, 10000);

              // Try to inject scrollbar-fixing CSS into the iframe content
              try {
                const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
                if (iframeDoc) {
                  // Create a style element
                  const style = iframeDoc.createElement('style');
                  style.textContent = `
                    html, body, div { overflow: hidden !important; }
                    ::-webkit-scrollbar { display: none !important; }
                    * { scrollbar-width: none !important; }

                    /* Fix HitsTV player aspect ratio */
                    .video-js, .plyr--video, [id*="player"], [class*="player"] {
                      position: relative !important;
                      height: auto !important;
                      aspect-ratio: 16/9 !important;
                      width: 100% !important;
                    }
                    video {
                      object-fit: contain !important;
                      height: auto !important;
                    }
                  `;
                  iframeDoc.head.appendChild(style);

                  // Try to modify any scrollable elements
                  const fixScrollbars = () => {
                    try {
                      const elements = iframeDoc.querySelectorAll('*');
                      elements.forEach((el: Element) => {
                        if (el instanceof HTMLElement) {
                          // Fix scrollbars
                          if (el.scrollHeight > el.clientHeight || el.scrollWidth > el.clientWidth) {
                            (el as HTMLElement).style.overflow = 'hidden';
                          }

                          // Fix HitsTV player specifically
                          if ((el.id && el.id.includes('hitstv')) ||
                              (el.className && typeof el.className === 'string' && el.className.includes('hitstv')) ||
                              (el.tagName === 'VIDEO') ||
                              (el.className && typeof el.className === 'string' &&
                               (el.className.includes('video-js') || el.className.includes('plyr')))) {

                            el.style.position = 'relative';
                            el.style.height = 'auto';
                            el.style.width = '100%';
                            el.style.aspectRatio = '16/9';

                            if (el.tagName === 'VIDEO') {
                              el.style.objectFit = 'contain';
                            }
                          }
                        }
                      });
                    } catch (e) {
                      // CORS might prevent this, just ignore
                    }
                  };

                  // Run immediately and repeatedly
                  fixScrollbars();
                  const intervalId = setInterval(fixScrollbars, 1000);
                  setTimeout(() => clearInterval(intervalId), 10000); // Stop after 10 seconds
                }
              } catch (e) {
                // CORS will prevent this in most cases, that's fine
                console.log("VidSrcPlayer: Unable to inject styles due to security restrictions");
              }
            }

            // Sometimes the iframe loads but content inside fails, set a check
            const checkContentTimer = setTimeout(() => {
              // If we find the iframe is empty or has errors, try next provider
              try {
                if (iframeRef.current && iframeRef.current.contentWindow) {
                  const iframeDoc = iframeRef.current.contentWindow.document;
                  // If the iframe has no body content or has error messages, try next provider
                  if (!iframeDoc.body.innerHTML ||
                      iframeDoc.body.innerHTML.includes("404") ||
                      iframeDoc.body.innerHTML.includes("error") ||
                      iframeDoc.body.innerHTML.includes("refused to connect")) {
                    console.log("VidSrcPlayer: Iframe loaded but content appears invalid, trying next provider");
                    handlePlayerError();
                  }
                }
              } catch (err) {
                // Cross-origin restrictions may prevent checking iframe content
                console.log("VidSrcPlayer: Unable to verify iframe content due to security restrictions");

                // For initial load, try next domain if we've tried less than all domains
                if (refreshCounter.current <= 1 && domainsTriedRef.current.length < vidsrcDomains.length) {
                  console.log("VidSrcPlayer: First load, trying next domain proactively");
                  // Only try auto-switching on desktop or if fewer than 2 switches have occurred on mobile
                  if (!isMobile() || providerSwitchCount.current < 2) {
                    handlePlayerError();
                  } else {
                    console.log("VidSrcPlayer: On mobile with multiple switches - waiting for manual action");
                  }
                }
              }
            }, isIOS() ? 5000 : (isMobile() ? 3000 : 2000)); // Longer timeout for iOS

            return () => clearTimeout(checkContentTimer);
          }}
          onError={() => {
            console.error(`VidSrcPlayer: iframe failed to load for ${type === 'movie' ? 'movie' : `S${actualSeason}E${actualEpisode}`}`);
            setLoading(false);
            setError("Video source failed to load. Trying next provider...");
            handlePlayerError(); // Auto try next domain when error occurs
          }}
        />
      </div>

      {error && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/70">
          <div className="bg-vista-dark p-4 rounded-lg max-w-md text-center">
            <h3 className="text-white font-medium mb-3">Media Unavailable</h3>
            <p className="text-gray-300 text-sm mb-4">{error}</p>
            <div className="flex flex-wrap gap-2 justify-center">
              {providerOptions.map(provider => (
                <button
                  key={provider.id}
                  onClick={() => switchToProvider(provider.id)}
                  className={`px-3 py-1.5 text-xs rounded-md ${
                    domain === provider.id
                      ? 'bg-vista-blue text-white'
                      : 'bg-gray-700 text-gray-200 hover:bg-gray-600'
                  }`}
                >
                  {provider.name}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
