import { NextRequest, NextResponse } from 'next/server';

/**
 * SSE (Server-Sent Events) route for watch party real-time updates
 * This is a placeholder implementation to resolve import errors
 */

// Store active SSE connections
const connections = new Map<string, Set<WritableStreamDefaultWriter>>();

/**
 * Broadcast a message to all clients in a specific party
 */
export async function broadcastToParty(partyId: string, data: any, eventType: string = 'message') {
  const partyConnections = connections.get(partyId);
  if (!partyConnections || partyConnections.size === 0) {
    console.log(`[SSE] No active connections for party ${partyId}`);
    return;
  }

  const message = `event: ${eventType}\ndata: ${JSON.stringify(data)}\n\n`;
  const encoder = new TextEncoder();
  const encodedMessage = encoder.encode(message);

  // Send to all connections in this party
  const deadConnections: WritableStreamDefaultWriter[] = [];
  
  for (const writer of partyConnections) {
    try {
      await writer.write(encodedMessage);
    } catch (error) {
      console.error('[SSE] Failed to write to connection:', error);
      deadConnections.push(writer);
    }
  }

  // Clean up dead connections
  deadConnections.forEach(writer => {
    partyConnections.delete(writer);
  });

  console.log(`[SSE] Broadcasted ${eventType} to ${partyConnections.size} connections in party ${partyId}`);
}

/**
 * Handle SSE connection requests
 */
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const partyId = searchParams.get('partyId');

  if (!partyId) {
    return NextResponse.json({ error: 'Party ID required' }, { status: 400 });
  }

  // Create a readable stream for SSE
  const stream = new ReadableStream({
    start(controller) {
      const encoder = new TextEncoder();
      
      // Send initial connection message
      const initialMessage = encoder.encode(`event: connected\ndata: ${JSON.stringify({ partyId })}\n\n`);
      controller.enqueue(initialMessage);

      // Create a writer for this connection
      const writer = new WritableStreamDefaultWriter(new WritableStream({
        write(chunk) {
          controller.enqueue(chunk);
        },
        close() {
          controller.close();
        }
      }));

      // Add to connections map
      if (!connections.has(partyId)) {
        connections.set(partyId, new Set());
      }
      connections.get(partyId)!.add(writer);

      console.log(`[SSE] New connection for party ${partyId}. Total connections: ${connections.get(partyId)!.size}`);

      // Handle connection cleanup
      request.signal.addEventListener('abort', () => {
        const partyConnections = connections.get(partyId);
        if (partyConnections) {
          partyConnections.delete(writer);
          if (partyConnections.size === 0) {
            connections.delete(partyId);
          }
        }
        controller.close();
        console.log(`[SSE] Connection closed for party ${partyId}`);
      });
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
    },
  });
}

/**
 * Handle preflight requests
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
