'use client'

import React, { useEffect, useState, useCallback, useRef, useMemo } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { ArrowLeft, Info, Share2, Plus, Star, AlertCircle, Users, Share, Loader2, MessageCircle, Send, Smile, Clock, X, AlertTriangle, Shield, SkipForward } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import VidSrcPlayer from '@/components/VidSrcPlayer'

// Debug: Test VidSrcPlayer import
console.log('[WatchPage] VidSrcPlayer component imported:', !!VidSrcPlayer);
import EpisodeList from '@/components/EpisodeList'
import RelatedContent from '@/components/RelatedContent'
import Footer from '@/components/Footer'
import Link from 'next/link'
import { getContentById, IContent } from '@/data/content'
import Image from 'next/image'
import { Navbar } from '@/components/Navbar'
import { ContentCardType } from '@/lib/content-utils'
import { pusherClient } from '@/lib/pusher-client'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  getMovieDetails,
  getTVShowDetails,
  getTVSeasonDetails,
  search
} from '@/lib/tmdb-api'
import { Skeleton } from '@/components/ui/skeleton'
import { useToast } from '@/components/ui/use-toast'
import { WatchPartyToast } from '@/components/WatchPartyToast'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ScrollArea, ScrollBar } from '@/components/ui/scroll-area'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { motion, AnimatePresence } from 'framer-motion'
import data from '@emoji-mart/data'
import Picker from '@emoji-mart/react'
import WatchPartyChat from "@/components/WatchPartyChat"
import { Episode } from '@/types/index'
import { scrollToElement } from '@/utils/scroll-utils'

interface WatchContentClientProps {
  contentId: string;
  initialForcePlay?: boolean;
  initialShowDetails?: boolean;
  partyMode?: {
    isPartyMode: boolean;
    partyId: string;
    isHost: boolean;
    connectionStatus: 'connecting' | 'connected' | 'error' | null;
  };
  contentType?: 'movie' | 'show' | string;
}

// Define extended content interface to match our needs
interface ExtendedContent extends IContent {
  seasonData?: {
    totalSeasons?: number;
    currentSeason?: number;
    currentEpisode?: number;
  };
  starring?: string | string[];
  creators?: string;
  seasonCount?: number;

  // OMDB-specific fields
  director?: string;
  actors?: string[];
  awards?: string;
  rated?: string;
  released?: string;
  metascore?: number;
  dataSource?: 'tmdb' | 'omdb' | 'both';
}

interface RelatedContent extends ContentCardType {
  overview?: string;
  posterPath?: string;
  rating?: number;
}

// Message interface for chat
interface ChatMessage {
  id: string;
  memberId: string;
  memberName: string;
  content: string;
  timestamp: string;
  type: 'chat' | 'system' | 'reaction';
}

// Common emoji reactions
const commonEmojis = ['👍', '👏', '❤️', '😂', '🔥', '🍿', '😮', '🎬', '👀', '🤔'];

// Format timestamp to show hour:minute
const formatMessageTime = (isoTimestamp: string) => {
  const date = new Date(isoTimestamp);
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
};

// Group messages by user and time proximity
const groupMessages = (messages: ChatMessage[]): ChatMessage[][] => {
  const groups: ChatMessage[][] = [];
  let currentGroup: ChatMessage[] = [];
  let lastSenderId = '';
  let lastTimestamp = 0;

  messages.forEach((message) => {
    // Always put system and reaction messages in their own group
    if (message.type === 'system' || message.type === 'reaction') {
      if (currentGroup.length > 0) {
        groups.push([...currentGroup]);
        currentGroup = [];
      }
      groups.push([message]);
      lastSenderId = '';
      return;
    }

    const messageTime = new Date(message.timestamp).getTime();
    // Group messages if from same sender and within 2 minutes
    const shouldGroup = message.memberId === lastSenderId &&
                        messageTime - lastTimestamp < 2 * 60 * 1000;

    if (shouldGroup) {
      currentGroup.push(message);
    } else {
      if (currentGroup.length > 0) {
        groups.push([...currentGroup]);
      }
      currentGroup = [message];
      lastSenderId = message.memberId;
    }

    lastTimestamp = messageTime;
  });

  // Add the last group if not empty
  if (currentGroup.length > 0) {
    groups.push(currentGroup);
  }

  return groups;
};

// LiveChat component for watch party - simplified to use the enhanced WatchPartyChat component
function LiveChat({ partyId }: { partyId: string }) {
  const [userId] = useState<string>(localStorage.getItem('watchPartyUserId') || '');
  const [username] = useState<string>(localStorage.getItem('watchPartyUserName') || `Guest-${Math.floor(Math.random() * 10000)}`);

  return (
    <div className="flex flex-col h-full rounded-lg overflow-hidden border border-vista-light/10 bg-gradient-to-b from-vista-dark to-vista-dark-lighter shadow-xl">
      {/* Header */}
      <div className="bg-vista-dark-lighter border-b border-vista-light/10 p-3 flex items-center justify-between bg-gradient-to-r from-vista-blue/20 to-transparent">
        <div className="flex items-center">
          <MessageCircle className="h-5 w-5 mr-2 text-vista-blue" />
          <h3 className="font-medium text-lg">Live Chat</h3>
        </div>
        <Badge variant="outline" className="bg-vista-blue/10 border-vista-blue/30 text-vista-blue flex items-center gap-1.5">
          <div className="h-2 w-2 rounded-full bg-green-500 animate-pulse" />
          <span>Live</span>
        </Badge>
      </div>

      {/* Enhanced Watch Party Chat */}
      <div className="flex-1">
        <WatchPartyChat
          partyId={partyId}
          userId={userId}
          username={username}
          showHeader={false}
          height="h-full"
          className="border-none"
        />
      </div>
    </div>
  );
}

export default function WatchContentClient({
  contentId,
  initialForcePlay,
  initialShowDetails,
  partyMode,
  contentType
}: WatchContentClientProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  const [content, setContent] = useState<ExtendedContent | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [relatedContent, setRelatedContent] = useState<RelatedContent[]>([])
  const [isLoadingRelated, setIsLoadingRelated] = useState(true)
  // Define our own Episode interface for internal use
  interface InternalEpisode {
    id: number | string;
    title: string;
    episodeNumber: number;
    stillPath: string | null;
    description: string;
    airDate: string;
    runtime: number | null;
    thumbnail?: string; // Added to match the Episode type from types/index
    seasonNumber?: number; // Added to match the Episode type from types/index
  }

  const [seasons, setSeasons] = useState<{ seasonNumber: number; episodes: InternalEpisode[] }[]>([])
  const [isLoadingSeasons, setIsLoadingSeasons] = useState(true)
  const [selectedSeason, setSelectedSeason] = useState<number>(1)
  const [activeSeason, setActiveSeason] = useState<number | null>(null)
  const [activeEpisode, setActiveEpisode] = useState<number | null>(null)
  const [previousSeason, setPreviousSeason] = useState<number | null>(null)
  const [currentEpisode, setCurrentEpisode] = useState<InternalEpisode | null>(null)
  const [seasonCount, setSeasonCount] = useState<number>(0) // Start with 0 and update when we get actual data from the API
  const [shouldRefreshPlayer, setShouldRefreshPlayer] = useState<boolean>(false)
  const [autoNextEnabled, setAutoNextEnabled] = useState<boolean>(false)
  const [watchPartyConnected, setWatchPartyConnected] = useState(false)
  const [partyConnectionAttempted, setPartyConnectionAttempted] = useState(false)
  const [notificationShown, setNotificationShown] = useState(false)
  const [playerRef, setPlayerRef] = useState<HTMLIFrameElement | null>(null)
  const playerCommandInProgress = useRef(false)
  const lastPlaybackUpdate = useRef<{time: number, isPlaying: boolean, updateCount: number} | null>(null)

  // Check if watch party notification has been shown in this session
  const [watchPartyNotificationShownInSession, setWatchPartyNotificationShownInSession] = useState(false)

  // Add useRef for the player container
  const playerContainerRef = useRef<HTMLDivElement>(null);

  // Initialize session storage check on component mount
  useEffect(() => {
    // Check if notification has been shown in this session
    const hasShownNotification = sessionStorage.getItem('watchPartyNotificationShown')
    if (hasShownNotification === 'true') {
      setWatchPartyNotificationShownInSession(true)
    }

    // Load auto-next preference from localStorage
    const savedAutoNext = localStorage.getItem('autoNextEnabled')
    if (savedAutoNext === 'true') {
      setAutoNextEnabled(true)
    }
  }, [])

  // Save auto-next preference to localStorage when it changes
  useEffect(() => {
    localStorage.setItem('autoNextEnabled', autoNextEnabled.toString())
  }, [autoNextEnabled])

  // Parse watch party params - from props or URL
  const watchPartyModeFromUrl = searchParams?.get('mode') === 'party'
  const watchPartyIdFromUrl = searchParams?.get('partyId')
  const isWatchPartyHost = searchParams?.get('isHost') === 'true'

  // Combine params from props and URL, with props taking precedence
  const isWatchParty = partyMode?.isPartyMode || watchPartyModeFromUrl
  const partyId = partyMode?.partyId || watchPartyIdFromUrl || ''
  const isHost = partyMode?.isHost || isWatchPartyHost
  const partyStatus = partyMode?.connectionStatus || (watchPartyConnected ? 'connected' : null)

  // Get specific search params needed for effects to avoid unnecessary re-renders
  const urlContentType = useMemo(() => searchParams?.get('contentType'), [searchParams])
  const urlMode = useMemo(() => searchParams?.get('mode'), [searchParams])
  const urlPartyId = useMemo(() => searchParams?.get('partyId'), [searchParams])
  const urlSeason = useMemo(() => searchParams?.get('season'), [searchParams])
  const urlEpisode = useMemo(() => searchParams?.get('episode'), [searchParams])
  const urlExiting = useMemo(() => searchParams?.get('exiting'), [searchParams])

  // Define fetchSeasonData and fetchRelatedContent *before* they are used in useEffect dependencies
  // Fetch season data
  const fetchSeasonData = useCallback(async (tmdbId: string, seasonNumber: number) => {
    console.log(`Fetching data for season ${seasonNumber} of show ${tmdbId}`);
    setIsLoadingSeasons(true);

    try {
      // First, get the TV show details to verify the season count
      const showDetails = await getTVShowDetails(tmdbId);
      console.log(`TV Show ${tmdbId} details:`, {
        name: showDetails.name,
        number_of_seasons: showDetails.number_of_seasons,
        seasons: showDetails.seasons ? showDetails.seasons.length : 'N/A'
      });

      // Update the season count based on the latest data
      if (showDetails.number_of_seasons) {
        console.log(`Updating season count to ${showDetails.number_of_seasons} from API`);
        setSeasonCount(showDetails.number_of_seasons);
      }

      // Now fetch the specific season data
      const fetchedSeasonData = await getTVSeasonDetails(tmdbId, seasonNumber);

      // Log the raw season data
      console.log(`Raw season ${seasonNumber} data:`, {
        seasonNumber: fetchedSeasonData.season_number,
        name: fetchedSeasonData.name,
        episodeCount: fetchedSeasonData.episodes ? fetchedSeasonData.episodes.length : 0
      });

      if (fetchedSeasonData && fetchedSeasonData.episodes && fetchedSeasonData.episodes.length > 0) {
        console.log(`Got season ${seasonNumber} data with ${fetchedSeasonData.episodes.length} episodes`);

        // Update seasons state while preserving any other seasons we've loaded
        setSeasons(prevSeasons => {
          // Find if we already have this season
          const seasonIndex = prevSeasons.findIndex(s => s.seasonNumber === seasonNumber);

          if (seasonIndex >= 0) {
            // Replace existing season
            const newSeasons = [...prevSeasons];
            newSeasons[seasonIndex] = {
              seasonNumber: seasonNumber,
              episodes: fetchedSeasonData.episodes.map((ep: TMDBEpisode) => ({
                id: ep.id,
                title: ep.name,
                episodeNumber: ep.episode_number,
                stillPath: ep.still_path ? `https://image.tmdb.org/t/p/w780${ep.still_path}` : null,
                description: ep.overview,
                airDate: ep.air_date,
                runtime: ep.runtime || null,
                thumbnail: ep.still_path ? `https://image.tmdb.org/t/p/w780${ep.still_path}` : '',
                seasonNumber: seasonNumber
              }))
            };
            return newSeasons;
          } else {
            // Add new season
            return [...prevSeasons, {
              seasonNumber: seasonNumber,
              episodes: fetchedSeasonData.episodes.map((ep: TMDBEpisode) => ({
                id: ep.id,
                title: ep.name,
                episodeNumber: ep.episode_number,
                stillPath: ep.still_path ? `https://image.tmdb.org/t/p/w780${ep.still_path}` : null,
                description: ep.overview,
                airDate: ep.air_date,
                runtime: ep.runtime || null,
                thumbnail: ep.still_path ? `https://image.tmdb.org/t/p/w780${ep.still_path}` : '',
                seasonNumber: seasonNumber
              }))
            }];
          }
        });
      } else {
        console.warn(`No episode data returned for season ${seasonNumber}`);

        // Mark this season as checked but with no episodes
        setSeasons(prevSeasons => {
          // Only add if we don't already have this season
          if (!prevSeasons.some(s => s.seasonNumber === seasonNumber)) {
            return [...prevSeasons, {
              seasonNumber: seasonNumber,
              episodes: [] // Empty episodes array
            }];
          }
          return prevSeasons;
        });
      }
    } catch (error) {
      console.error(`Error fetching season ${seasonNumber} data:`, error);

      // Mark this season as checked but with no episodes
      setSeasons(prevSeasons => {
        // Only add if we don't already have this season
        if (!prevSeasons.some(s => s.seasonNumber === seasonNumber)) {
          return [...prevSeasons, {
            seasonNumber: seasonNumber,
            episodes: [] // Empty episodes array
          }];
        }
        return prevSeasons;
      });
    } finally {
      setIsLoadingSeasons(false);
    }
  }, []);

  // Interface for TMDb episode data
  interface TMDBEpisode {
    id: number;
    name: string;
    episode_number: number;
    season_number: number;
    overview: string;
    still_path: string | null;
    air_date: string;
    runtime?: number;
  }

  // Fetch related content (wrapped in useCallback)
  const fetchRelatedContent = useCallback(async (tmdbId: string, type: string) => {
    setIsLoadingRelated(true);
    try {
      console.log(`Fetching related content for ${type} with TMDB ID: ${tmdbId}`);

      // Use our API endpoint instead of direct TMDB calls
      const response = await fetch(`/api/related-content?id=${tmdbId}&type=${type === 'movie' ? 'movie' : 'show'}`);

      if (!response.ok) {
        throw new Error(`Failed to fetch related content: ${response.status}`);
      }

      const data = await response.json();
      console.log(`Fetched ${data.length} related content items`);

      // Map to our app format if needed
      interface RelatedContentItem {
        id: number;
        title: string;
        type: string;
        posterUrl: string | null;
        year?: string;
        overview: string;
        voteAverage: number;
      }

      const mapped = data.map((item: RelatedContentItem) => ({
        id: item.id,
        title: item.title || 'Unknown Title',
        type: item.type === 'movie' ? 'movies' : 'shows',
        imagePath: item.posterUrl || null,
        year: item.year,
        overview: item.overview,
        rating: item.voteAverage,
        tmdbId: item.id
      }));

      setRelatedContent(mapped);
    } catch (error) {
      console.error('Error fetching related content:', error);
      // Set empty array to avoid undefined errors
      setRelatedContent([]);
    } finally {
      setIsLoadingRelated(false);
    }
  }, []); // Dependencies: none, uses setters which are stable

  // Get playback params from URL, with prop overrides taking priority
  const forcePlay = initialForcePlay !== undefined
    ? initialForcePlay
    : searchParams?.get('forcePlay') === 'true'

  const showDetails = initialShowDetails !== undefined
    ? initialShowDetails
    : searchParams?.get('showDetails') === 'true'

  // Function to handle playback control for watch party with improved reliability
  const handleWatchPartyPlaybackControl = useCallback((isPlaying: boolean, currentTime?: number) => {
    if (!playerRef || !isWatchParty) return;

    console.log(`[WatchContentClient] Sending ${isPlaying ? 'play' : 'pause'} command to player${currentTime !== undefined ? ` at time ${currentTime}` : ''}`);

    // Set a flag to prevent sending updates back to server immediately
    playerCommandInProgress.current = true;

    try {
      // If we have a current time, seek to that position first with multiple attempts
      if (currentTime !== undefined) {
        // Send seek command multiple times with increasing delays to ensure it gets through
        for (let i = 0; i < 3; i++) {
          setTimeout(() => {
            if (playerRef && playerRef.contentWindow) {
              playerRef.contentWindow.postMessage(
                JSON.stringify({ action: 'seek', time: currentTime }),
                '*'
              );
              console.log(`[WatchContentClient] Sent seek command (attempt ${i+1}/3) to time ${currentTime}`);
            }
          }, i * 100); // Send at 0ms, 100ms, and 200ms
        }

        // Then play or pause as needed - also with multiple attempts
        const playPauseAction = isPlaying ? 'play' : 'pause';
        for (let i = 0; i < 3; i++) {
          setTimeout(() => {
            if (playerRef && playerRef.contentWindow) {
              playerRef.contentWindow.postMessage(
                JSON.stringify({ action: playPauseAction }),
                '*'
              );
              console.log(`[WatchContentClient] Sent ${playPauseAction} command (attempt ${i+1}/3)`);
            }
          }, 300 + i * 100); // Start after seek commands
        }
      } else {
        // Just send play/pause command with multiple attempts
        const playPauseAction = isPlaying ? 'play' : 'pause';
        for (let i = 0; i < 3; i++) {
          setTimeout(() => {
            if (playerRef && playerRef.contentWindow) {
              playerRef.contentWindow.postMessage(
                JSON.stringify({ action: playPauseAction }),
                '*'
              );
              console.log(`[WatchContentClient] Sent ${playPauseAction} command (attempt ${i+1}/3)`);
            }
          }, i * 100); // Send at 0ms, 100ms, and 200ms
        }
      }
    } catch (error) {
      console.error('[WatchContentClient] Error applying playback control:', error);
    }

    // Clear the flag after a longer delay to allow all commands to be processed
    setTimeout(() => {
      playerCommandInProgress.current = false;
      console.log('[WatchContentClient] Playback command completed, accepting new commands');
    }, 1200); // Ignore player state updates for 1.2 seconds after sending commands
  }, [playerRef, isWatchParty]);

  // Listen for watch party playback updates
  useEffect(() => {
    if (!isWatchParty || !partyId) return;

    console.log('[WatchContentClient] Setting up watch party connections for party:', partyId);

    // Set up both SSE and Pusher for redundancy
    let eventSource: EventSource | null = null;

    try {
      eventSource = new EventSource(`/api/watch-party/sse?partyId=${partyId}`);
      console.log('[WatchContentClient] SSE connection established');
    } catch (error) {
      console.error('[WatchContentClient] Error creating SSE connection:', error);
    }

    // Function to handle playback updates from any source with improved reliability
    interface PlaybackUpdateData {
      currentTime: number;
      isPlaying: boolean;
      updateCount?: number;
    }

    const handlePlaybackUpdate = (data: PlaybackUpdateData) => {
      // Skip if we're the one who sent this update
      if (playerCommandInProgress.current) {
        console.log('[WatchContentClient] Ignoring playback update during command execution');
        return;
      }

      // Always log the update for debugging
      console.log('[WatchContentClient] Received playback update:', data);

      // Store this update regardless of whether we process it
      // This ensures we have the latest state even if we don't apply it immediately
      lastPlaybackUpdate.current = {
        time: data.currentTime,
        isPlaying: data.isPlaying,
        updateCount: data.updateCount || 0
      };

      // Apply the update to the player with a small delay to ensure stability
      // This helps prevent race conditions when multiple updates arrive in quick succession
      setTimeout(() => {
        console.log('[WatchContentClient] Processing playback update:', data);
        handleWatchPartyPlaybackControl(data.isPlaying, data.currentTime);
      }, 50);
    };

    // SSE message handler
    const messageHandler = (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);
        console.log('[WatchContentClient] Received SSE message:', data);

        // Handle playback updates
        if (data.type === 'playback-update' && data.playback) {
          handlePlaybackUpdate(data.playback);
        }
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    // SSE error handler
    const errorHandler = () => {
      console.error('SSE connection error');
      if (eventSource) {
        eventSource.close();
      }

      // Reconnect after a delay
      setTimeout(() => {
        try {
          const newEventSource = new EventSource(`/api/watch-party/sse?partyId=${partyId}`);
          eventSource = newEventSource;

          newEventSource.addEventListener('message', messageHandler);
          newEventSource.addEventListener('error', errorHandler);
          console.log('[WatchContentClient] SSE connection reestablished');
        } catch (error) {
          console.error('[WatchContentClient] Failed to reconnect SSE:', error);
        }
      }, 5000);
    };

    // Set up SSE event listeners if we have a connection
    if (eventSource) {
      eventSource.addEventListener('message', messageHandler);
      eventSource.addEventListener('error', errorHandler);
    }

    // Set up Pusher event listener for redundancy
    let pusherChannel: { bind: (event: string, callback: (data: PlaybackUpdateData) => void) => void; unbind: (event: string) => void } | null = null;

    try {
      pusherChannel = pusherClient.subscribe(`watch-party-${partyId}`);
      console.log('[WatchContentClient] Pusher subscription created for channel:', `watch-party-${partyId}`);

      pusherChannel.bind('playback-update', (data: PlaybackUpdateData) => {
        console.log('[WatchContentClient] Received Pusher playback update:', data);
        handlePlaybackUpdate(data);
      });
    } catch (error) {
      console.error('[WatchContentClient] Error setting up Pusher:', error);
    }

    // Periodically check for playback state to ensure we're in sync
    const syncInterval = setInterval(async () => {
      if (!isHost) {
        try {
          const response = await fetch(`/api/watch-party/playback?partyId=${partyId}`);
          if (response.ok) {
            const data = await response.json();
            if (data.success && data.playback) {
              handlePlaybackUpdate(data.playback);
            }
          }
        } catch (error) {
          console.error('[WatchContentClient] Error fetching playback state:', error);
        }
      }
    }, 30000); // Check every 30 seconds instead of 10 to reduce Pusher usage

    return () => {
      console.log('[WatchContentClient] Cleaning up watch party connections');
      // Clean up SSE
      if (eventSource) {
        eventSource.removeEventListener('message', messageHandler);
        eventSource.removeEventListener('error', errorHandler);
        eventSource.close();
      }

      // Clean up Pusher
      if (pusherChannel) {
        try {
          pusherChannel.unbind('playback-update');
          pusherClient.unsubscribe(`watch-party-${partyId}`);
        } catch (error) {
          console.error('[WatchContentClient] Error cleaning up Pusher:', error);
        }
      }

      // Clean up sync interval
      clearInterval(syncInterval);
    };
  }, [isWatchParty, partyId, handleWatchPartyPlaybackControl, isHost]);

  // Listen for player events and send updates to watch party
  useEffect(() => {
    if (!isWatchParty || !partyId) return;

    console.log('[WatchContentClient] Setting up player event listener, isHost:', isHost);

    // Throttle function to limit update frequency
    let lastUpdateTime = 0;
    let lastReportedTime = 0;
    const regularUpdateThrottleMs = 3000; // 3 seconds between regular updates
    const importantUpdateThrottleMs = 200; // Keep responsive for play/pause
    const timeUpdateThreshold = 3; // Only send time updates when difference is > 3 seconds

    const handlePlayerEvent = (event: MessageEvent) => {
      if (!event.data || typeof event.data !== 'string') return;

      try {
        const data = JSON.parse(event.data);

        // Handle player state updates for watch party
        if (data.type === 'playerState') {
          // Skip if we're currently processing a command
          if (playerCommandInProgress.current) {
            console.log('[WatchContentClient] Ignoring player state update during command execution');
            return;
          }

          // Only send updates if we're the host
          if (isHost) {
            const now = Date.now();
            // Throttle updates to prevent flooding but ensure important state changes are sent
            const isImportantStateChange = lastPlaybackUpdate.current &&
                                          lastPlaybackUpdate.current.isPlaying !== data.isPlaying;

            // Check if time change is significant enough to send an update
            const currentTime = data.currentTime || 0;
            const isSignificantTimeChange = Math.abs((lastReportedTime || 0) - currentTime) > timeUpdateThreshold;

            // Determine if we should send an update based on type and throttling
            const shouldUpdate = isImportantStateChange ?
              (now - lastUpdateTime > importantUpdateThrottleMs) : // For play/pause, use shorter throttle
              (isSignificantTimeChange && now - lastUpdateTime > regularUpdateThrottleMs); // For time updates

            if (!shouldUpdate) {
              return;
            }

            // Update tracking variables
            lastUpdateTime = now;
            lastReportedTime = currentTime;

            console.log('[WatchContentClient] Sending playback update to watch party:', {
              currentTime: data.currentTime,
              isPlaying: data.isPlaying,
              isImportantStateChange
            });

            // Send update to the dedicated playback API
            fetch('/api/watch-party/playback', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                partyId,
                currentTime: data.currentTime,
                isPlaying: data.isPlaying,
                isHost: true
              }),
              // Ensure the request is not cached
              cache: 'no-store'
            }).catch(error => {
              console.error('[WatchContentClient] Error sending playback update:', error);
            });
          }
        }
      } catch (error) {
        // Ignore parsing errors
      }
    };

    window.addEventListener('message', handlePlayerEvent);

    // Initial playback state fetch for joiners
    if (!isHost) {
      console.log('[WatchContentClient] Fetching initial playback state as joiner');

      // Try multiple times with exponential backoff
      const fetchInitialState = async (attempt = 1) => {
        try {
          // Add a timestamp to prevent caching
          const response = await fetch(`/api/watch-party/playback?partyId=${partyId}&_t=${Date.now()}`);

          // Parse the response
          let data;
          try {
            data = await response.json();
          } catch (parseError) {
            // If we can't parse JSON, create a basic error object
            data = {
              success: false,
              error: `Server returned ${response.status}`
            };
          }

          if (data.success && data.playback) {
            console.log('[WatchContentClient] Initial playback state:', data.playback);

            // If this is a default state and we're early in our retry attempts,
            // we might want to try again to get a real state
            if (data.isDefault && attempt < 3) {
              console.log('[WatchContentClient] Received default state, will retry');
              setTimeout(() => fetchInitialState(attempt + 1), 1000 * attempt);
              return;
            }

            // Apply the playback state
            handleWatchPartyPlaybackControl(data.playback.isPlaying, data.playback.currentTime);
          } else {
            console.log('[WatchContentClient] No valid playback state available:', data);
            // If no state yet and we haven't tried too many times, try again
            if (attempt < 5) {
              console.log(`[WatchContentClient] Will retry in ${attempt}s (attempt ${attempt}/5)`);
              setTimeout(() => fetchInitialState(attempt + 1), 1000 * attempt);
            } else {
              console.log('[WatchContentClient] Max retries reached, using default state');
              // After max retries, just use a default state
              handleWatchPartyPlaybackControl(false, 0);
            }
          }
        } catch (error) {
          console.error('[WatchContentClient] Error processing playback state:', error);
          // Retry with backoff
          if (attempt < 5) {
            console.log(`[WatchContentClient] Will retry in ${attempt}s (attempt ${attempt}/5)`);
            setTimeout(() => fetchInitialState(attempt + 1), 1000 * attempt);
          } else {
            console.log('[WatchContentClient] Max retries reached, using default state');
            // After max retries, just use a default state
            handleWatchPartyPlaybackControl(false, 0);
          }
        }
      };

      // Start the fetch process
      fetchInitialState();
    }

    return () => {
      window.removeEventListener('message', handlePlayerEvent);
    };
  }, [isWatchParty, partyId, isHost, handleWatchPartyPlaybackControl]);

  // DEBUG: Add comprehensive logging for auto-next
const handleAutoNext = useCallback(async (newSeason: number, newEpisode: number) => {
  console.log(`🎬 [WatchContentClient] AUTO-NEXT CALLBACK TRIGGERED! S${newSeason}E${newEpisode}`);
  console.log(`🎬 [WatchContentClient] Current state: S${activeSeason}E${activeEpisode}`);
  
  // Always update regardless of current state for debugging
  console.log(`🎬 [WatchContentClient] FORCING UPDATE to S${newSeason}E${newEpisode}`);
  
  // Update our state immediately
  setActiveSeason(newSeason);
  setActiveEpisode(newEpisode);
  
  // Force a re-render to ensure UI updates
  setForceRenderCounter(prev => {
    const newCount = prev + 1;
    console.log(`🎬 [WatchContentClient] Force render counter: ${prev} -> ${newCount}`);
    return newCount;
  });
  
  // Update browser URL
  const url = new URL(window.location.href);
  url.searchParams.set('season', newSeason.toString());
  url.searchParams.set('episode', newEpisode.toString());
  window.history.replaceState({}, '', url.toString());
  console.log(`🎬 [WatchContentClient] Updated URL: ${url.toString()}`);
  
  // Fetch season data if we don't have it yet
  if (content?.tmdbId) {
    const existingSeason = seasons.find(s => s.seasonNumber === newSeason);
    if (!existingSeason || existingSeason.episodes.length === 0) {
      console.log(`🎬 [WatchContentClient] Fetching data for new season ${newSeason}`);
      await fetchSeasonData(content.tmdbId.toString(), newSeason);
    } else {
      console.log(`🎬 [WatchContentClient] Season ${newSeason} already loaded with ${existingSeason.episodes.length} episodes`);
    }
  }
  
  // Scroll to player
  setTimeout(() => {
    scrollToElement('video-player', 'smooth', 80);
  }, 500);
}, [activeSeason, activeEpisode, content?.tmdbId, seasons, fetchSeasonData]);

  // Connect to the watch party if needed
  useEffect(() => {
    // Check if we're exiting the party
    const isExitingParty = searchParams?.get('exiting') === 'true'

    // Only try to connect once to prevent loops, and don't connect if exiting
    if (!isWatchParty || !partyId || partyConnectionAttempted || isExitingParty) {
      return
    }

    const connectToParty = async () => {
      try {
        setPartyConnectionAttempted(true)
        console.log(`[WatchContentClient] Attempting to connect to watch party: ${partyId}`)

        // Get username from local storage or generate a random one
        const username = localStorage.getItem('watchPartyUserName') || `Guest-${Math.floor(Math.random() * 10000)}`

        // Generate a consistent user ID or retrieve from storage
        const userId = localStorage.getItem('watchPartyUserId') || `user-${Math.random().toString(36).substring(2, 9)}`
        // Store it for future use
        if (!localStorage.getItem('watchPartyUserId')) {
          localStorage.setItem('watchPartyUserId', userId)
        }

        // Call the API to join or check the party
        const response = await fetch('/api/watch-party', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            event: 'join-watch-party',
            data: {
              partyId,
              memberId: userId,
              memberName: username
            }
          }),
        })

        if (!response.ok) {
          console.error(`[WatchContentClient] Failed to connect to party ${partyId}`)

          // Only show notification if not already shown
          if (!notificationShown) {
            setNotificationShown(true)
            toast({
              title: "Watch Party Not Found",
              description: "The watch party couldn't be found. You'll be returned to the watch party page.",
              variant: "destructive"
            })

            // Reset notification flag after a delay
            setTimeout(() => setNotificationShown(false), 2000)
          }

          // Allow a moment to see the error, then redirect
          setTimeout(() => {
            router.push('/watch-party')
          }, 3000)

          return
        }

        // Party connection successful
        const wasAlreadyConnected = watchPartyConnected;
        setWatchPartyConnected(true);
        console.log(`[WatchContentClient] Successfully connected to watch party: ${partyId}`);

        // Only show notification if not already shown in this session AND not already connected
        if (!watchPartyNotificationShownInSession && !wasAlreadyConnected) {
          // Mark as shown in this session
          setWatchPartyNotificationShownInSession(true);
          sessionStorage.setItem('watchPartyNotificationShown', 'true');

          // Set local notification state
          setNotificationShown(true);

          // Show custom toast
          toast({
            title: "Watch Party Connected",
            description: "You are now watching together with others in this party.",
            variant: "success"
          });

          // Reset notification flag after a longer delay
          setTimeout(() => setNotificationShown(false), 5000);
        }
      } catch (error) {
        console.error(`[WatchContentClient] Error connecting to watch party:`, error)

        // Only show notification if not already shown
        if (!notificationShown) {
          setNotificationShown(true)
          toast({
            title: "Connection Error",
            description: "There was a problem connecting to the watch party. You can continue watching solo.",
            variant: "destructive"
          })

          // Reset notification flag after a delay
          setTimeout(() => setNotificationShown(false), 2000)
        }
      }
    }

    connectToParty()
  }, [isWatchParty, partyId, router, toast, partyConnectionAttempted, urlExiting, notificationShown, searchParams, watchPartyConnected, watchPartyNotificationShownInSession])

  // Fetch content details
  useEffect(() => {
    const fetchContent = async () => {
      try {
        // Log parameters to debug the issue
        console.log('[Watch Content] Loading content:', {
          contentId,
          contentType: urlContentType,
          mode: urlMode,
          partyId: urlPartyId,
          season: urlSeason,
          episode: urlEpisode
        });

        // Determine if we're fetching movie or TV show details based on content type in URL
        // Make sure to use the explicit contentType passed from props if available
        const resolvedContentType = contentType || urlContentType || 'movie';

        // Additional logging to help with debugging content type issues
        console.log(`[Watch Content] Resolved content type: "${resolvedContentType}" from props: ${contentType}, URL: ${urlContentType}`);
        console.log(`[Watch Content] URL parameters:`, { urlContentType, urlMode, urlPartyId, urlSeason, urlEpisode });

        // Use our combined API route that includes both TMDB and OMDB data
        // IMPORTANT: Don't redeclare contentType - use a different variable name
        const normalizedContentType = resolvedContentType.toLowerCase() === 'show' ||
                           resolvedContentType.toLowerCase() === 'tv' ? 'show' : 'movie';

        // Make sure we're using the correct contentId from the URL
        console.log(`[Watch Content] Fetching ${normalizedContentType} details for ID: ${contentId}`);

        try {
          // Use our API route that combines TMDB and OMDB data
          console.log(`[Watch Content] Fetching from API: /api/content?id=${contentId}&type=${normalizedContentType}`);
          const response = await fetch(`/api/content?id=${contentId}&type=${normalizedContentType}`);

          if (!response.ok) {
            console.error(`[Watch Content] API error: ${response.status}`);
            // Try to get more detailed error information
            try {
              const errorData = await response.text();
              console.error(`[Watch Content] API error details:`, errorData);
            } catch (e) {
              console.error(`[Watch Content] Could not parse error details:`, e);
            }
            throw new Error(`Failed to fetch content details: ${response.status}`);
          }

          const fetchedContent = await response.json();
          console.log('[Watch Content] Fetched content:', fetchedContent.title);

          // Map content to our app format
          if (fetchedContent) {
            const mappedContent: ExtendedContent = {
              id: fetchedContent.id.toString(),
              title: fetchedContent.title,
              type: normalizedContentType as 'movie' | 'show',
              year: fetchedContent.year || '',
              posterPath: fetchedContent.posterPath || '',
              overview: fetchedContent.overview || '',
              rating: fetchedContent.rating || 0,
              runtime: fetchedContent.runtime || null,
              genres: fetchedContent.genres || [],
              tmdbId: fetchedContent.tmdbId || fetchedContent.id.toString(),
              imdbId: fetchedContent.imdbId || null,
              seasonCount: fetchedContent.seasons || 0,
              // OMDB-specific fields
              director: fetchedContent.director,
              actors: fetchedContent.actors,
              awards: fetchedContent.awards,
              rated: fetchedContent.rated,
              released: fetchedContent.released,
              metascore: fetchedContent.metascore,
              dataSource: fetchedContent.dataSource || 'tmdb'
            };

            setContent(mappedContent);

            // Set season count if it's a show
            if ((resolvedContentType.toLowerCase() === 'show' || resolvedContentType.toLowerCase() === 'tv')) {
              // Log the raw data to debug
              console.log(`[Watch Content] Raw TV show data:`, {
                id: fetchedContent.id,
                title: fetchedContent.name || fetchedContent.title,
                number_of_seasons: fetchedContent.number_of_seasons,
                seasons: fetchedContent.seasons ? fetchedContent.seasons.length : 'N/A'
              });

              // Make sure we're getting the correct season count
              const actualSeasonCount = fetchedContent.number_of_seasons || 0;
              console.log(`[Watch Content] Setting season count to ${actualSeasonCount}`);
              setSeasonCount(actualSeasonCount);

              // Get initial season and episode from URL
              const urlSeason = parseInt(searchParams?.get('season') || '1');
              const urlEpisode = parseInt(searchParams?.get('episode') || '1');

              console.log(`[Watch Content] Using season ${urlSeason}, episode ${urlEpisode}`);

              // Set the active season and episode without validation or redirection
              // The EpisodeList component will only show seasons with actual episodes
              setActiveSeason(urlSeason);
              setActiveEpisode(urlEpisode);
            } else {
              // Default values for movies
              setActiveSeason(1);
              setActiveEpisode(1);
            }

            // Fetch related content
            if (fetchedContent.id) {
              fetchRelatedContent(fetchedContent.id.toString(), resolvedContentType);

              // Fetch initial season data for shows
              if (resolvedContentType.toLowerCase() === 'show' || resolvedContentType.toLowerCase() === 'tv') {
                const initialSeason = parseInt(searchParams?.get('season') || '1');
                await fetchSeasonData(fetchedContent.id.toString(), initialSeason);
              }
            }
          }
        } catch (contentError) {
          console.error('[Watch Content] Error fetching content details:', contentError);

          // Try to use TMDB API directly as a fallback
          try {
            console.log('[Watch Content] Attempting to use TMDB API directly as fallback');

            let tmdbData;
            if (normalizedContentType === 'movie') {
              tmdbData = await getMovieDetails(contentId);
            } else {
              tmdbData = await getTVShowDetails(contentId);
            }

            console.log('[Watch Content] Successfully fetched from TMDB fallback:', tmdbData?.title || tmdbData?.name);

            // Map TMDB data to our app format
            const mappedContent: ExtendedContent = {
              id: contentId,
              title: tmdbData.title || tmdbData.name,
              type: normalizedContentType as 'movie' | 'show',
              year: tmdbData.release_date ? tmdbData.release_date.substring(0, 4) :
                   (tmdbData.first_air_date ? tmdbData.first_air_date.substring(0, 4) : ''),
              posterPath: tmdbData.poster_path,
              overview: tmdbData.overview || '',
              rating: tmdbData.vote_average || 0,
              runtime: tmdbData.runtime || null,
              genres: tmdbData.genres?.map((g: { name: string }) => g.name) || [],
              tmdbId: contentId,
              dataSource: 'tmdb'
            };

            setContent(mappedContent);

            // Set season count if it's a show
            if (normalizedContentType === 'show') {
              // Log the raw data to debug
              console.log(`[Watch Content] Raw TV show data (fallback):`, {
                id: tmdbData.id,
                title: tmdbData.name || tmdbData.title,
                number_of_seasons: tmdbData.number_of_seasons,
                seasons: tmdbData.seasons ? tmdbData.seasons.length : 'N/A'
              });

              // Make sure we're getting the correct season count
              const actualSeasonCount = tmdbData.number_of_seasons || 0;
              console.log(`[Watch Content] Setting season count to ${actualSeasonCount} (fallback)`);
              setSeasonCount(actualSeasonCount);

              // Get initial season and episode from URL
              const urlSeason = parseInt(searchParams?.get('season') || '1');
              const urlEpisode = parseInt(searchParams?.get('episode') || '1');

              console.log(`[Watch Content] Using season ${urlSeason}, episode ${urlEpisode}`);

              // Set the active season and episode without validation or redirection
              // The EpisodeList component will only show seasons with actual episodes
              setActiveSeason(urlSeason);
              setActiveEpisode(urlEpisode);
            } else {
              // Default values for movies
              setActiveSeason(1);
              setActiveEpisode(1);
            }
          } catch (fallbackError) {
            console.error('[Watch Content] Fallback also failed:', fallbackError);

            // If we're in watch party mode and failed to fetch, show a more specific error
            if (isWatchParty) {
              toast({
                title: "Content Error",
                description: "Could not load content details for this watch party. The content may no longer be available.",
                variant: "destructive"
              });
            } else {
              // Check if contentError is an Error before accessing message
              const errorMessage = fallbackError instanceof Error ? fallbackError.message : String(fallbackError);
              setError(`Failed to load content: ${errorMessage}. Fallback also failed.`);
            }
          }
        }
      } catch (error) {
        console.error('[Watch Content] Error in content fetch effect:', error);
      } finally {
        console.log('[Watch Content] ===== SETTING LOADING FALSE =====');
        console.log('[Watch Content] Content loaded, VidSrcPlayer should now render');
        setLoading(false);
      }
    };

    if (contentId) {
      fetchContent();
    }
  }, [contentId, urlContentType, urlMode, urlPartyId, urlSeason, urlEpisode, contentType, isWatchParty, toast, fetchRelatedContent, fetchSeasonData, searchParams]);

  // Interface for episode data returned from TMDb API
  interface TMDBEpisode {
    id: number;
    name: string;
    episode_number: number;
    still_path: string | null;
    overview: string;
    air_date: string;
    runtime?: number;
  }

  // Handle back button click with prevention of full page refresh
  const handleBack = useCallback((e?: React.MouseEvent) => {
    if (e) e.preventDefault();

    // If in watch party mode, add exiting flag
    if (isWatchParty) {
      router.push(`/watch-party?exiting=true`);
    } else {
      router.back();
    }
  }, [isWatchParty, router]);

  // Handle click on related content with prevention of full page refresh
  const handleRelatedContentClick = (e: React.MouseEvent, type: string, id: string) => {
    e.preventDefault();

    console.log(`Navigating to related content: type=${type}, id=${id}, event=`, e);

    // Convert type format to match what the API expects
    const apiContentType = type === 'movies' ? 'movie' : 'show';

    // Use router.push but prevent the default navigate behavior first
    const newPath = `/watch/${id}?contentType=${apiContentType}`;
    console.log(`Pushing to new path: ${newPath}`);
    router.push(newPath);
  };

  // Set up event listener for season changes
  useEffect(() => {
    // Define type-safe event handlers
    const handleSeasonChange = (event: Event) => {
      // Cast the event to CustomEvent to access the detail property
      const customEvent = event as CustomEvent<{ season: number }>;
      const { season } = customEvent.detail;
      console.log(`Season change event received: season=${season}`);

      // Just update the state and URL parameters without navigation
      setActiveSeason(season);
      setActiveEpisode(1);
      setSelectedSeason(season); // Make sure selected season is updated too
      setShouldRefreshPlayer(false); // Don't refresh player, just change season

      // Update URL without navigation
      try {
        const url = new URL(window.location.href);
        url.searchParams.set('season', season.toString());
        url.searchParams.set('episode', '1');
        window.history.replaceState({}, '', url.toString());
      } catch (error) {
        console.error('Error updating URL:', error);
      }

      // Always fetch season data when a season change event is received
      if (content?.tmdbId) {
        console.log(`Fetching data for season ${season} after season change event`);
        fetchSeasonData(content.tmdbId.toString(), season);
      }
    };

    // Set up event listener for episode changes
    const handleEpisodeChange = (event: Event) => {
      // Cast the event to CustomEvent to access the detail property
      const customEvent = event as CustomEvent<{ season: number; episode: number }>;
      const { season, episode } = customEvent.detail;
      console.log(`Episode change event received: season=${season}, episode=${episode}`);

      // Update state
      setActiveSeason(season);
      setActiveEpisode(episode);

      // This is an explicit episode selection, so we should refresh the player
      setShouldRefreshPlayer(true);

      // Update URL without navigation
      try {
        const url = new URL(window.location.href);
        url.searchParams.set('season', season.toString());
        url.searchParams.set('episode', episode.toString());
        window.history.replaceState({}, '', url.toString());

        // Log the updated URL for debugging
        console.log(`Updated URL: ${url.toString()}`);
      } catch (error) {
        console.error('Error updating URL:', error);
      }

      // Fetch season data if needed
      if (content?.tmdbId) {
        // Check if we already have this season's data
        const existingSeason = seasons.find(s => s.seasonNumber === season);

        // Only fetch if we don't have the season data or if it has no episodes
        if (!existingSeason || existingSeason.episodes.length === 0) {
          console.log(`New season ${season} not loaded yet, fetching data...`);
          fetchSeasonData(content.tmdbId.toString(), season);
        } else {
          console.log(`Season ${season} already loaded with ${existingSeason.episodes.length} episodes`);
          // Make sure we're not in loading state
          setIsLoadingSeasons(false);
        }
      }

      // Force player refresh after a small delay to ensure state is updated
      setTimeout(() => {
        console.log('Forcing player refresh after episode change');
        setShouldRefreshPlayer(true);
      }, 100);

      // Scroll to the player after episode change with a slight delay
      setTimeout(() => {
        scrollToElement('video-player', 'smooth', 80); // 80px offset for navbar
      }, 200);
    };

    // Add event listeners
    window.addEventListener('seasonChange', handleSeasonChange);
    window.addEventListener('episodeChange', handleEpisodeChange);

    // Clean up
    return () => {
      window.removeEventListener('seasonChange', handleSeasonChange);
      window.removeEventListener('episodeChange', handleEpisodeChange);
    };
  }, [content?.tmdbId, fetchSeasonData, seasons, setIsLoadingSeasons]);

  // State for tracking current player state
  const [playerState, setPlayerState] = useState({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    buffering: false,
    lastUpdateTime: 0
  });

  // Force re-render counter for debugging
  const [forceRenderCounter, setForceRenderCounter] = useState(0);

  // Reset shouldRefreshPlayer after a short delay to allow the player to process the refresh
  useEffect(() => {
    if (shouldRefreshPlayer) {
      const timer = setTimeout(() => {
        setShouldRefreshPlayer(false);
      }, 1000); // Reset after 1 second

      return () => clearTimeout(timer);
    }
  }, [shouldRefreshPlayer]);

  // Update currentEpisode when activeSeason or activeEpisode changes
  useEffect(() => {
    if (content?.type === 'show' && activeSeason && activeEpisode && seasons.length > 0) {
      const currentSeasonData = seasons.find(s => s.seasonNumber === activeSeason);
      if (currentSeasonData) {
        const episodeData = currentSeasonData.episodes.find(ep => ep.episodeNumber === activeEpisode);
        if (episodeData) {
          console.log(`[WatchContentClient] Updating currentEpisode to S${activeSeason}E${activeEpisode}: ${episodeData.title}`);
          setCurrentEpisode(episodeData);
        } else {
          console.log(`[WatchContentClient] Episode S${activeSeason}E${activeEpisode} not found in season data`);
          setCurrentEpisode(null);
        }
      } else {
        console.log(`[WatchContentClient] Season ${activeSeason} not found in seasons data`);
        setCurrentEpisode(null);
      }
    } else if (content?.type === 'movie') {
      // For movies, clear the current episode
      setCurrentEpisode(null);
    }
  }, [content?.type, activeSeason, activeEpisode, seasons]);

  // State for tracking if a command is in progress
  const [commandInProgress, setCommandInProgress] = useState(false);

  // Function to get current player state
  const updatePlayerState = useCallback(() => {
    if (!playerRef) return;

    try {
      const videoElement = playerRef.contentWindow?.document?.querySelector('video');
      if (videoElement) {
        setPlayerState(prev => ({
          ...prev,
          isPlaying: !videoElement.paused,
          currentTime: videoElement.currentTime || 0,
          duration: videoElement.duration || 0,
          lastUpdateTime: Date.now()
        }));
      }
    } catch (error) {
      console.warn('[WatchParty] Could not get player state:', error);
    }
  }, [playerRef]);

  // Update player state periodically
  useEffect(() => {
    if (!playerRef || !isWatchParty) return;

    const interval = setInterval(() => {
      updatePlayerState();
    }, 1000);

    return () => clearInterval(interval);
  }, [playerRef, isWatchParty, updatePlayerState]);

  // Format time for display (MM:SS)
  const formatTime = useCallback((seconds: number) => {
    if (isNaN(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }, []);

  // Function to handle host playback controls with improved reliability
  const handleHostPlayPause = useCallback(() => {
    if (!playerRef || !isHost) return;

    // Set command in progress to prevent multiple rapid commands
    setCommandInProgress(true);

    // Get current player state
    const isCurrentlyPlaying = playerRef.contentWindow?.document?.querySelector('video')?.paused === false;
    const newPlayState = !isCurrentlyPlaying;

    console.log(`[WatchParty] Host toggling playback to: ${newPlayState ? 'play' : 'pause'}`);

    // Update local state immediately for responsive UI
    setPlayerState(prev => ({
      ...prev,
      isPlaying: newPlayState,
      lastUpdateTime: Date.now()
    }));

    // Send command to player with multiple attempts to ensure it gets through
    // This is crucial for reliable playback control
    for (let i = 0; i < 3; i++) {
      setTimeout(() => {
        if (playerRef && playerRef.contentWindow) {
          console.log(`[WatchParty] Sending ${newPlayState ? 'play' : 'pause'} command to player (attempt ${i+1}/3)`);
          playerRef.contentWindow.postMessage(
            JSON.stringify({ action: newPlayState ? 'play' : 'pause' }),
            '*'
          );

          // Also try to directly control the video element if possible
          try {
            const videoElement = playerRef.contentWindow.document.querySelector('video');
            if (videoElement) {
              if (newPlayState) {
                videoElement.play();
              } else {
                videoElement.pause();
              }
              console.log(`[WatchParty] Direct video element control ${newPlayState ? 'play' : 'pause'} (attempt ${i+1}/3)`);
            }
          } catch (directControlError) {
            // CORS will likely prevent this, which is fine
            console.log('[WatchParty] Could not directly control video element due to security restrictions');
          }
        }
      }, i * 200); // Send at 0ms, 200ms, and 400ms with increasing delays
    }

    // Send update to all party members
    try {
      // Get current time from player if possible
      let currentTime = 0;
      try {
        const videoElement = playerRef.contentWindow?.document?.querySelector('video');
        if (videoElement) {
          currentTime = videoElement.currentTime || 0;
        }
      } catch (timeError) {
        console.warn('[WatchParty] Could not get current time from player:', timeError);
      }

      // Show toast notification immediately for better UX
      toast({
        title: newPlayState ? "Playback Started" : "Playback Paused",
        description: `You ${newPlayState ? 'started' : 'paused'} playback for all party members.`,
        duration: 2000
      });

      // Send update to server
      fetch('/api/watch-party/playback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          partyId,
          currentTime,
          isPlaying: newPlayState,
          isHost: true
        }),
        cache: 'no-store' // Ensure the request is not cached
      })
      .then(response => {
        // Don't try to parse JSON if response is not ok
        if (!response.ok) {
          throw new Error(`Failed to update playback state: ${response.status}`);
        }
        // Only try to parse JSON if we need the response data
        // In this case we don't actually need it, so we can skip this step
        return { success: true };
      })
      .catch(error => {
        console.error('[WatchParty] Error sending playback update:', error);
        toast({
          title: "Sync Warning",
          description: "There was an issue syncing with other members, but your playback has been updated.",
          variant: "destructive"
        });
      })
      .finally(() => {
        // Clear command in progress after a longer delay to ensure all commands are processed
        setTimeout(() => setCommandInProgress(false), 1000);
      });
    } catch (error) {
      console.error('[WatchParty] Error in host playback control:', error);
      setCommandInProgress(false);
    }

    // Use handleWatchPartyPlaybackControl as a backup method
    // This uses the established reliable method that works for party members
    setTimeout(() => {
      handleWatchPartyPlaybackControl(newPlayState, undefined);
    }, 500); // Slight delay to avoid conflicts with direct commands

  }, [playerRef, isHost, partyId, toast, handleWatchPartyPlaybackControl]);

  // Function to seek to a specific position for all members
  const handleHostSeek = useCallback((seekDirection: 'backward' | 'forward') => {
    if (!playerRef || !isHost || commandInProgress) return;

    // Set command in progress to prevent multiple rapid commands
    setCommandInProgress(true);

    // Default seek amount in seconds
    const seekAmount = seekDirection === 'forward' ? 10 : -10;

    try {
      // Get current time from player if possible
      let currentTime = 0;
      try {
        const videoElement = playerRef.contentWindow?.document?.querySelector('video');
        if (videoElement) {
          currentTime = (videoElement.currentTime || 0) + seekAmount;
          // Ensure we don't go below 0
          currentTime = Math.max(0, currentTime);
        }
      } catch (timeError) {
        console.warn('[WatchParty] Could not get current time from player:', timeError);
        setCommandInProgress(false);
        return; // Don't proceed if we can't get the current time
      }

      console.log(`[WatchParty] Host seeking to: ${currentTime}s (${seekDirection})`);

      // Update local state immediately for responsive UI
      setPlayerState(prev => ({
        ...prev,
        currentTime,
        lastUpdateTime: Date.now()
      }));

      // Send seek command to player with multiple attempts to ensure it gets through
      for (let i = 0; i < 3; i++) {
        setTimeout(() => {
          if (playerRef && playerRef.contentWindow) {
            console.log(`[WatchParty] Sending seek command to player (attempt ${i+1}/3)`);
            playerRef.contentWindow.postMessage(
              JSON.stringify({ action: 'seek', time: currentTime }),
              '*'
            );

            // Also try to directly control the video element if possible
            try {
              const videoElement = playerRef.contentWindow.document.querySelector('video');
              if (videoElement) {
                videoElement.currentTime = currentTime;
                console.log(`[WatchParty] Direct video element seek to ${currentTime}s (attempt ${i+1}/3)`);
              }
            } catch (directControlError) {
              // CORS will likely prevent this, which is fine
              console.log('[WatchParty] Could not directly control video element due to security restrictions');
            }
          }
        }, i * 200); // Send at 0ms, 200ms, and 400ms with increasing delays
      }

      // Show toast notification immediately for better UX
      toast({
        title: "Playback Adjusted",
        description: `Seeked ${Math.abs(seekAmount)} seconds ${seekDirection === 'forward' ? 'forward' : 'backward'} for all members.`,
        duration: 2000
      });

      // Get current play state
      const isCurrentlyPlaying = playerRef.contentWindow?.document?.querySelector('video')?.paused === false;

      // Send update to server
      fetch('/api/watch-party/playback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          partyId,
          currentTime,
          isPlaying: isCurrentlyPlaying,
          isHost: true
        }),
        cache: 'no-store' // Ensure the request is not cached
      })
      .then(response => {
        // Don't try to parse JSON if response is not ok
        if (!response.ok) {
          throw new Error(`Failed to update playback position: ${response.status}`);
        }
        // Skip JSON parsing since we don't need the response data
        return { success: true };
      })
      .catch(error => {
        console.error('[WatchParty] Error sending seek update:', error);
        toast({
          title: "Sync Warning",
          description: "There was an issue syncing with other members, but your playback has been updated.",
          variant: "destructive"
        });
      })
      .finally(() => {
        // Clear command in progress after a longer delay
        setTimeout(() => setCommandInProgress(false), 1000);
      });
    } catch (error) {
      console.error('[WatchParty] Error in host seek control:', error);
      setCommandInProgress(false);
    }

    // Use handleWatchPartyPlaybackControl as a backup method
    // This uses the established reliable method that works for party members
    setTimeout(() => {
      try {
        // Get the current time again to ensure we have the latest value
        let backupTime = 0;
        try {
          const videoElement = playerRef.contentWindow?.document?.querySelector('video');
          if (videoElement) {
            backupTime = videoElement.currentTime || 0;
          }
        } catch (timeError) {
          console.warn('[WatchParty] Could not get current time for backup method:', timeError);
        }
        handleWatchPartyPlaybackControl(true, backupTime);
      } catch (error) {
        console.error('[WatchParty] Error in backup playback control:', error);
      }
    }, 500); // Slight delay to avoid conflicts with direct commands

  }, [playerRef, isHost, partyId, toast, commandInProgress, handleWatchPartyPlaybackControl]);

  // Function to handle seeking to a specific time using the progress bar
  const handleProgressBarSeek = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!playerRef || !isHost || commandInProgress || !playerState.duration) return;

    // Set command in progress to prevent multiple rapid commands
    setCommandInProgress(true);

    // Calculate the new time based on click position
    const progressBar = e.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickPosition = (e.clientX - rect.left) / rect.width;
    const newTime = playerState.duration * clickPosition;

    try {
      console.log(`[WatchParty] Host seeking to specific time: ${newTime}s`);

      // Update local state immediately for responsive UI
      setPlayerState(prev => ({
        ...prev,
        currentTime: newTime,
        lastUpdateTime: Date.now()
      }));

      // Send seek command to player with multiple attempts to ensure it gets through
      for (let i = 0; i < 3; i++) {
        setTimeout(() => {
          if (playerRef && playerRef.contentWindow) {
            console.log(`[WatchParty] Sending progress bar seek command to player (attempt ${i+1}/3)`);
            playerRef.contentWindow.postMessage(
              JSON.stringify({ action: 'seek', time: newTime }),
              '*'
            );

            // Also try to directly control the video element if possible
            try {
              const videoElement = playerRef.contentWindow.document.querySelector('video');
              if (videoElement) {
                videoElement.currentTime = newTime;
                console.log(`[WatchParty] Direct video element seek to ${newTime}s (attempt ${i+1}/3)`);
              }
            } catch (directControlError) {
              // CORS will likely prevent this, which is fine
              console.log('[WatchParty] Could not directly control video element due to security restrictions');
            }
          }
        }, i * 200); // Send at 0ms, 200ms, and 400ms with increasing delays
      }

      // Show toast notification immediately for better UX
      toast({
        title: "Playback Position Changed",
        description: `Set playback position to ${formatTime(newTime)} for all members.`,
        duration: 2000
      });

      // Get current play state
      const isCurrentlyPlaying = playerRef.contentWindow?.document?.querySelector('video')?.paused === false;

      // Send update to server
      fetch('/api/watch-party/playback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          partyId,
          currentTime: newTime,
          isPlaying: isCurrentlyPlaying,
          isHost: true
        }),
        cache: 'no-store' // Ensure the request is not cached
      })
      .then(response => {
        // Don't try to parse JSON if response is not ok
        if (!response.ok) {
          throw new Error(`Failed to update playback position: ${response.status}`);
        }
        // Skip JSON parsing since we don't need the response data
        return { success: true };
      })
      .catch(error => {
        console.error('[WatchParty] Error sending seek update:', error);
        // Don't show an error toast since we already showed a success toast
        // and the local playback has been updated
      })
      .finally(() => {
        // Clear command in progress after a longer delay
        setTimeout(() => setCommandInProgress(false), 1000);
      });
    } catch (error) {
      console.error('[WatchParty] Error in host seek control:', error);
      setCommandInProgress(false);
    }

    // Use handleWatchPartyPlaybackControl as a backup method
    // This uses the established reliable method that works for party members
    setTimeout(() => {
      handleWatchPartyPlaybackControl(true, newTime);
    }, 500); // Slight delay to avoid conflicts with direct commands

  }, [playerRef, isHost, partyId, toast, commandInProgress, playerState.duration, formatTime, handleWatchPartyPlaybackControl]);

  // Function to render watch party status with modern design
  const renderWatchPartyStatus = () => {
    if (!isWatchParty) return null;

    return (
      <div className="w-full mb-6 relative z-10">
        <div className="bg-gradient-to-r from-blue-950/80 to-indigo-950/80 border border-blue-500/30 rounded-xl p-5 flex flex-col gap-5 shadow-lg">
          {/* Header with connection status */}
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className="bg-blue-500/20 p-2 rounded-full mr-3">
                <Users className="h-5 w-5 text-blue-400" />
              </div>
              <div>
                <span className="font-medium text-white">
                  Watch Party
                </span>
                <div className="flex items-center mt-1">
                  <div className={`h-2 w-2 rounded-full ${watchPartyConnected ? 'bg-green-500 animate-pulse' : 'bg-yellow-500 animate-pulse'} mr-2`} />
                  <span className="text-xs text-blue-200">
                    {watchPartyConnected ? 'Connected' : 'Connecting...'}
                  </span>
                </div>
              </div>
            </div>

            {isHost && (
              <Badge variant="outline" className="bg-blue-500/20 border-blue-500/30 text-blue-300 px-3 py-1">
                <Shield className="h-3 w-3 mr-2" />
                Host Controls
              </Badge>
            )}
          </div>

          {/* Host playback controls - only visible to host */}
          {isHost && watchPartyConnected && (
            <div className="bg-blue-900/30 backdrop-blur-sm border border-blue-500/30 rounded-xl overflow-hidden">
              {/* Progress bar */}
              <div className="relative w-full h-2 bg-blue-950/50 cursor-pointer" onClick={handleProgressBarSeek}>
                <div
                  className="absolute top-0 left-0 h-full bg-gradient-to-r from-blue-500 to-indigo-500"
                  style={{ width: `${playerState.duration ? (playerState.currentTime / playerState.duration) * 100 : 0}%` }}
                />
                <div
                  className="absolute top-0 h-full w-2 bg-white rounded-full shadow-md"
                  style={{
                    left: `calc(${playerState.duration ? (playerState.currentTime / playerState.duration) * 100 : 0}% - 1px)`,
                    display: playerState.duration ? 'block' : 'none'
                  }}
                />
              </div>

              <div className="p-4">
                {/* Time display */}
                <div className="flex justify-between text-xs text-blue-300 mb-3">
                  <span>{formatTime(playerState.currentTime)}</span>
                  <span>{formatTime(playerState.duration)}</span>
                </div>

                <div className="flex flex-col gap-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-blue-200">Host Playback Controls</h3>
                    <Badge variant="outline" className="bg-blue-500/20 border-blue-400/30 text-blue-200 text-xs">
                      Controls everyone's playback
                    </Badge>
                  </div>

                  <div className="flex justify-center items-center gap-3">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-full hover:bg-blue-800/50 text-white h-10 w-10"
                      onClick={() => handleHostSeek('backward')}
                      disabled={commandInProgress}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polygon points="19 20 9 12 19 4 19 20"></polygon>
                        <line x1="5" y1="19" x2="5" y2="5"></line>
                      </svg>
                    </Button>

                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-full bg-blue-500 hover:bg-blue-600 text-white h-14 w-14 flex items-center justify-center"
                      onClick={handleHostPlayPause}
                      disabled={commandInProgress}
                    >
                      {playerState.isPlaying ? (
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <rect x="6" y="4" width="4" height="16"></rect>
                          <rect x="14" y="4" width="4" height="16"></rect>
                        </svg>
                      ) : (
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <polygon points="5 3 19 12 5 21 5 3"></polygon>
                        </svg>
                      )}
                    </Button>

                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-full hover:bg-blue-800/50 text-white h-10 w-10"
                      onClick={() => handleHostSeek('forward')}
                      disabled={commandInProgress}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <polygon points="5 4 15 12 5 20 5 4"></polygon>
                        <line x1="19" y1="5" x2="19" y2="19"></line>
                      </svg>
                    </Button>
                  </div>

                  {/* Status indicator */}
                  {commandInProgress && (
                    <div className="flex items-center justify-center text-xs text-blue-300 mt-1">
                      <div className="h-1.5 w-1.5 rounded-full bg-blue-400 animate-pulse mr-2"></div>
                      Syncing with party members...
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {watchPartyConnected && (
            <div className="flex flex-col gap-3 w-full mt-2">
              <div className="grid grid-cols-2 gap-3">
                <Button
                  size="sm"
                  variant="ghost"
                  className="bg-blue-900/30 hover:bg-blue-800/50 text-white border border-blue-500/20 rounded-lg h-10"
                  onClick={() => {
                    router.push(`/watch-party/${partyId}`)
                  }}
                >
                  <Users className="h-4 w-4 mr-2" />
                  Party Details
                </Button>

                <Button
                  size="sm"
                  variant="ghost"
                  className="bg-blue-900/30 hover:bg-blue-800/50 text-white border border-blue-500/20 rounded-lg h-10"
                  onClick={() => {
                    // Copy watch party invite link
                    const inviteLink = `${window.location.origin}/watch/${contentId}?mode=party&partyId=${partyId}`;
                    navigator.clipboard.writeText(inviteLink);

                    // Only show notification if not already shown
                    if (!notificationShown) {
                      setNotificationShown(true);
                      toast({
                        title: "Invite Link Copied",
                        description: "Share this link to invite others to your watch party!"
                      });

                      // Reset notification flag after a delay
                      setTimeout(() => setNotificationShown(false), 2000);
                    }
                  }}
                >
                  <Share className="h-4 w-4 mr-2" />
                  Share Invite
                </Button>
              </div>

              <div className="grid grid-cols-2 gap-3">
                {isHost ? (
                  <>
                    <Button
                      size="sm"
                      variant="ghost"
                      className="bg-blue-900/30 hover:bg-blue-800/50 text-white border border-blue-500/20 rounded-lg h-10"
                      onClick={() => {
                        // Navigate back to dashboard with refresh parameter
                        router.push(`/watch-party?refresh=true&exiting=true`);
                      }}
                    >
                      <ArrowLeft className="h-4 w-4 mr-2" />
                      Exit Party
                    </Button>

                    <Button
                      size="sm"
                      variant="ghost"
                      className="bg-red-900/30 hover:bg-red-800/50 text-red-300 border border-red-500/20 rounded-lg h-10"
                      onClick={async () => {
                        // End the party via API
                        try {
                          // Show a toast to indicate we're ending the party
                          toast({
                            title: "Ending watch party",
                            description: "Please wait while we clean up..."
                          });

                          // First, mark the party as ended in the database
                          const response = await fetch('/api/watch-party', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              event: 'update-watch-party',
                              data: {
                                partyId: partyId,
                                updates: {
                                  status: 'ended',
                                  endedAt: new Date().toISOString()
                                }
                              }
                            }),
                          });

                          if (!response.ok) {
                            throw new Error('Failed to end party');
                          }

                          // Wait a moment to ensure the update is processed
                          await new Promise(resolve => setTimeout(resolve, 300));

                          // Then make a separate API call to delete the party
                          await fetch('/api/watch-party', {
                            method: 'POST',
                            headers: {
                              'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                              event: 'delete-watch-party',
                              data: {
                                partyId: partyId
                              }
                            }),
                          });

                          toast({
                            title: "Watch party ended",
                            description: "The watch party has been successfully ended."
                          });

                          // Navigate back to dashboard with refresh parameter
                          router.push(`/watch-party?refresh=true&exiting=true`);
                        } catch (error) {
                          console.error(`[WatchParty] Error ending party:`, error);

                          toast({
                            title: "Error ending party",
                            description: "There was a problem ending the watch party.",
                            variant: "destructive"
                          });
                        }
                      }}
                    >
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      End Party
                    </Button>
                  </>
                ) : (
                  <Button
                    size="sm"
                    variant="ghost"
                    className="col-span-2 bg-blue-900/30 hover:bg-blue-800/50 text-white border border-blue-500/20 rounded-lg h-10"
                    onClick={() => {
                      // Leave the party via API
                      try {
                        // Call the leave party API
                        fetch('/api/watch-party', {
                          method: 'POST',
                          headers: {
                            'Content-Type': 'application/json',
                          },
                          body: JSON.stringify({
                            event: 'leave-watch-party',
                            data: {
                              partyId: partyId,
                              memberId: localStorage.getItem('watchPartyUserId')
                            }
                          }),
                        });

                        // Navigate back to dashboard with refresh parameter
                        router.push(`/watch-party?refresh=true&exiting=true`);
                      } catch (error) {
                        console.error(`[WatchParty] Error leaving party:`, error);

                        // Still navigate back even if there's an error
                        router.push(`/watch-party?refresh=true&exiting=true`);
                      }
                    }}
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Leave Party
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Add useEffect to access the iframe after the VidSrcPlayer renders
  useEffect(() => {
    if (playerContainerRef.current) {
      const iframe = playerContainerRef.current.querySelector('iframe');
      if (iframe) {
        setPlayerRef(iframe);
      }
    }
  }, [content, activeSeason, activeEpisode, shouldRefreshPlayer]);

  if (loading) {
    console.log('[WatchPage] Still loading, showing skeleton...');
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col gap-8">
          <div className="w-full bg-black mb-6">
            <Skeleton className="aspect-video w-full max-w-5xl mx-auto" />
          </div>

          <div className="flex flex-col lg:flex-row gap-8">
            <div className="lg:w-2/3">
              <Skeleton className="h-10 w-40 mb-4" />
              <Skeleton className="h-6 w-full mb-2" />
              <Skeleton className="h-6 w-4/5 mb-4" />
              <Skeleton className="h-[400px] w-full rounded-lg" />
            </div>

            <div className="lg:w-1/3">
              <Skeleton className="h-10 w-40 mb-4" />
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-4">
                {[1, 2, 3, 4].map((i) => (
                  <Skeleton key={i} className="h-32 w-full rounded-lg" />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!content) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <div className="bg-vista-dark rounded-lg p-8 max-w-lg mx-auto">
          <h2 className="text-xl font-semibold mb-4">Content Not Found</h2>
          <p className="text-vista-light mb-6">We couldn't find the content you're looking for. It may have been removed or you may have followed an incorrect link.</p>
          <Button onClick={() => router.push('/')}>Return Home</Button>
        </div>
      </div>
    );
  }

  // Check if content has required IDs for VidSrc
  const hasStreamingIds = content && (content.imdbId || content.tmdbId)
  const isShow = content.type === 'show'

  // Debug content type
  console.log('Content ID:', contentId)
  console.log('Content Type:', content.type)
  console.log('Title:', content.title)
  console.log('Is identified as a show:', isShow)
  console.log('Watch Party Mode:', isWatchParty ? 'YES' : 'NO')

  // Log whether we're in direct play mode
  const isDirectPlayMode = forcePlay || isWatchParty || (hasStreamingIds && !showDetails)
  console.log('Direct play mode:', isDirectPlayMode ? 'YES' : 'NO')

  // Render the player section
  const renderPlayer = () => {
    return (
      <div
        className="relative w-full bg-black overflow-hidden rounded-lg"
        style={{
          aspectRatio: "16/9",
          maxWidth: "100%",
          border: "none",
          overflow: "hidden" // Ensure both x and y overflow are hidden
        }}
        ref={playerContainerRef}
      >
        {/* Player controls overlay */}
        <div className="absolute top-4 left-4 z-20 flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="rounded-full bg-black/40 hover:bg-black/60"
            onClick={() => router.push('/')}
          >
            <ArrowLeft className="h-5 w-5 text-white" />
          </Button>
          {content?.title && (
            <h1 className="text-white text-lg font-medium hidden sm:block">
              {content.title}
              {content.type === 'show' && activeEpisode && activeSeason && (
                <span className="text-sm font-normal ml-2 opacity-80">
                  • S{activeSeason}E{activeEpisode}
                </span>
              )}
            </h1>
          )}
        </div>

        {/* Player */}
        {(() => {
          const playerProps = {
            imdbId: content?.imdbId,
            tmdbId: content?.tmdbId,
            type: content?.type || 'movie',
            season: activeSeason || parseInt(searchParams?.get('season') || '1'),
            episode: activeEpisode || parseInt(searchParams?.get('episode') || '1'),
            className: "w-full",
            height: "100%",
            shouldRefresh: shouldRefreshPlayer,
            autoNext: autoNextEnabled && content?.type === 'show',
            onAutoNext: handleAutoNext
          };

          console.log('[WatchPage] ===== RENDERING VIDSRCPLAYER =====');
          console.log('[WatchPage] Content available:', !!content);
          console.log('[WatchPage] Content data:', content);
          console.log('[WatchPage] Player props:', playerProps);

          // Validate critical props
          if (!playerProps.imdbId && !playerProps.tmdbId) {
            console.error('[WatchPage] CRITICAL: No IMDB or TMDB ID in props!');
          }
          if (!playerProps.type) {
            console.error('[WatchPage] CRITICAL: No content type in props!');
          }

          return (
            <VidSrcPlayer
              imdbId={content?.imdbId}
              tmdbId={content?.tmdbId}
              type={content?.type || 'movie'} // Ensure type is always defined
              season={activeSeason || parseInt(searchParams?.get('season') || '1')}
              episode={activeEpisode || parseInt(searchParams?.get('episode') || '1')}
              className="w-full"
              height="100%"
              shouldRefresh={shouldRefreshPlayer}
              autoNext={autoNextEnabled && content?.type === 'show'}
              onAutoNext={handleAutoNext}
            />
          );
        })()}
      </div>
    );
  };

  return (
    <div className="flex flex-col min-h-screen bg-vista-dark text-vista-light">
      <Navbar />

      {/* Main container with increased spacing */}
      <div className="container mx-auto px-4 pt-20 pb-24">
        <div className="flex items-center flex-wrap gap-4 mb-6">
          <Button
            variant="ghost"
            size="icon"
            onClick={handleBack}
            className="hover:bg-vista-dark-lighter rounded-full"
          >
            <ArrowLeft className="h-6 w-6" />
          </Button>
          <h1 className="text-2xl md:text-3xl font-bold truncate">
            {content?.title}
            {content?.year ? ` (${content.year})` : ''}
          </h1>

          {isWatchParty && (
            <Badge variant="outline" className="ml-2 bg-blue-500/20 text-blue-400 border-blue-500/30">
              <Users className="h-3 w-3 mr-1" />
              Watch Party
            </Badge>
          )}
        </div>

        {/* Watch party status banner with improved positioning */}
        {renderWatchPartyStatus()}

        {/* Enhanced grid layout for content and player */}
        <div key={`content-${activeSeason}-${activeEpisode}-${forceRenderCounter}`} className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          {/* Content details - expanded and more prominent */}
          {content && (
            <>
              <div className="lg:col-span-1 space-y-6 bg-vista-dark-lighter rounded-xl p-6 order-2 lg:order-1">
                <div className="flex flex-col items-center md:items-start gap-6">
                  {content.posterPath && (
                    <div className="w-48 h-72 relative rounded-lg overflow-hidden shadow-lg">
                      <Image
                        src={content.posterPath}
                        alt={content.title || "Content poster"}
                        fill
                        className="object-cover"
                        priority
                      />
                    </div>
                  )}

                  <div className="flex-1 w-full">
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Badge variant="outline" className="bg-vista-blue/10 border-vista-blue/30 text-vista-blue">
                        {content.type === 'movie' ? 'Movie' : 'TV Series'}
                      </Badge>
                      {content.year && (
                        <Badge variant="outline" className="bg-vista-dark-lighter border-vista-light/20">
                          {content.year}
                        </Badge>
                      )}
                      {(() => {
                        // For TV shows, show episode runtime; for movies, show movie runtime
                        let runtimeToShow = content.runtime;
                        
                        if (content.type === 'show' && seasons.length > 0) {
                          // Find current episode runtime
                          const currentSeason = seasons.find(s => s.seasonNumber === activeSeason);
                          if (currentSeason) {
                            const currentEpisode = currentSeason.episodes.find(ep => ep.episodeNumber === activeEpisode);
                            console.log('[WatchContentClient] Runtime calculation - activeSeason:', activeSeason, 'activeEpisode:', activeEpisode, 'currentEpisode runtime:', currentEpisode?.runtime);
                            if (currentEpisode && currentEpisode.runtime) {
                              runtimeToShow = currentEpisode.runtime;
                            }
                          }
                        }
                        
                        return runtimeToShow ? (
                          <Badge variant="outline" className="bg-vista-dark-lighter border-vista-light/20">
                            {Math.floor(runtimeToShow / 60)}h {runtimeToShow % 60}m
                          </Badge>
                        ) : null;
                      })()}
                      {content.rating && (
                        <Badge variant="secondary" className="bg-vista-dark-lighter">
                          <Star className="h-3 w-3 mr-1 fill-yellow-400 text-yellow-400" />
                          {content.rating.toFixed(1)}
                        </Badge>
                      )}
                    </div>

                    <div className="mb-4">
                      <h3 className="text-lg font-medium mb-2">Overview</h3>
                      <p className="text-vista-light/80 leading-relaxed">{content.overview || "No description available."}</p>
                    </div>

                    {content.genres && content.genres.length > 0 && (
                      <div className="mb-4">
                        <h3 className="text-lg font-medium mb-2">Genres</h3>
                        <div className="flex flex-wrap gap-2">
                          {content.genres?.map((genre) => (
                            <Badge key={genre} variant="secondary" className="bg-vista-dark-lighter">
                              {genre}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* OMDB-specific information */}
                    <div className="space-y-3 mt-4">
                      {content.director && (
                        <div>
                          <h3 className="text-sm font-medium text-vista-light/70">Director</h3>
                          <p className="text-vista-light">{content.director}</p>
                        </div>
                      )}

                      {content.actors && content.actors.length > 0 && (
                        <div>
                          <h3 className="text-sm font-medium text-vista-light/70">Cast</h3>
                          <p className="text-vista-light">{content.actors.join(', ')}</p>
                        </div>
                      )}

                      {content.awards && (
                        <div>
                          <h3 className="text-sm font-medium text-vista-light/70">Awards</h3>
                          <p className="text-vista-light">{content.awards}</p>
                        </div>
                      )}

                      {content.rated && (
                        <div>
                          <h3 className="text-sm font-medium text-vista-light/70">Rated</h3>
                          <p className="text-vista-light">{content.rated}</p>
                        </div>
                      )}

                      {content.released && (
                        <div>
                          <h3 className="text-sm font-medium text-vista-light/70">Released</h3>
                          <p className="text-vista-light">{content.released}</p>
                        </div>
                      )}

                      {content.metascore && content.metascore > 0 && (
                        <div>
                          <h3 className="text-sm font-medium text-vista-light/70">Metascore</h3>
                          <p className="text-vista-light">{content.metascore}</p>
                        </div>
                      )}

                      {content.dataSource && (
                        <div className="mt-4">
                          <Badge variant="outline" className="bg-vista-dark-lighter border-vista-light/20">
                            Data: {content.dataSource === 'tmdb' ? 'TMDB' :
                                  content.dataSource === 'omdb' ? 'OMDB/IMDB' :
                                  'TMDB + OMDB/IMDB'}
                          </Badge>
                        </div>
                      )}
                    </div>

                    {content?.type === 'show' && (
                      <>
                        <div className="text-sm text-vista-light/90 mt-4 p-3 bg-vista-dark rounded-lg">
                          <div className="flex flex-col gap-1">
                            <div>
                              <span className="font-medium">Currently Playing:</span> Season {activeSeason || parseInt(searchParams?.get('season') || '1')},
                              Episode {activeEpisode || parseInt(searchParams?.get('episode') || '1')}
                            </div>
                            {(() => {
                              const currentSeason = seasons.find(s => s.seasonNumber === activeSeason);
                              const currentEpisode = currentSeason?.episodes.find(ep => ep.episodeNumber === activeEpisode);
                              console.log('[WatchContentClient] Currently Playing - activeSeason:', activeSeason, 'activeEpisode:', activeEpisode, 'currentEpisode:', currentEpisode);
                              return currentEpisode && currentEpisode.title && (
                                <div className="text-vista-blue font-medium">
                                  {currentEpisode.title}
                                </div>
                              );
                            })()}
                          </div>
                        </div>

                        {/* Auto-next control */}
                        <div className="mt-4 p-3 bg-vista-dark rounded-lg border border-vista-light/10">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <SkipForward className="h-4 w-4 text-vista-blue" />
                              <span className="text-sm font-medium">Auto-play next episode</span>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              className={`transition-all duration-200 ${
                                autoNextEnabled 
                                  ? 'bg-vista-blue/20 hover:bg-vista-blue/30 text-vista-blue' 
                                  : 'bg-vista-light/10 hover:bg-vista-light/20 text-vista-light/70'
                              }`}
                              onClick={() => setAutoNextEnabled(!autoNextEnabled)}
                            >
                              {autoNextEnabled ? 'ON' : 'OFF'}
                            </Button>
                          </div>
                          <p className="text-xs text-vista-light/60 mt-2">
                            Automatically play the next episode when the current one ends
                          </p>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Video player container - slightly reduced size */}
              <div id="video-player" className="lg:col-span-2 order-1 lg:order-2">
                {renderPlayer()}
              </div>
            </>
          )}
        </div>

        {/* Live Chat - Only shown in Watch Party mode */}
        {isWatchParty && partyId && (
          <div className="mb-6 h-[500px]">
            <LiveChat partyId={partyId} />
          </div>
        )}

        {/* TV Show Episodes - Only for TV Shows */}
        {content?.type === 'show' && (
          <div className="mt-8">
            <EpisodeList
              seasons={seasons.map(season => ({
                seasonNumber: season.seasonNumber,
                episodes: season.episodes.map(ep => ({
                  id: String(ep.id), // Convert to string to match Episode type
                  title: ep.title,
                  episodeNumber: ep.episodeNumber,
                  description: ep.description,
                  airDate: ep.airDate,
                  runtime: ep.runtime === null ? undefined : ep.runtime,
                  thumbnail: ep.thumbnail || ep.stillPath || '',
                  seasonNumber: ep.seasonNumber || season.seasonNumber
                }))
              }))}
              showTitle={content?.title || ''}
              seasonCount={seasonCount}
              isLoading={isLoadingSeasons}
              imdbId={content?.imdbId}
              tmdbId={content?.tmdbId}
              contentId={contentId}
              contentType="show"
              activeSeason={activeSeason || undefined}
              activeEpisode={activeEpisode || undefined}
            />
          </div>
        )}

        {/* Related Content - Show for both movies and TV shows */}
        {!isWatchParty && relatedContent.length > 0 && (
          <div className="mt-8">
            <RelatedContent
              title="More Like This"
              contents={relatedContent.map(content => ({
                ...content,
                // Add a click handler via the contentClickHandler prop
                onClick: (e: React.MouseEvent) => handleRelatedContentClick(e, content.type, content.id)
              }))}
            />
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
}