PS C:\Users\<USER>\Desktop\StreamVista> npm run dev

> stream-vista@0.1.0 dev
> next dev -H 0.0.0.0

 ⚠ Port 3000 is in use, trying 3001 instead.
   ▲ Next.js 15.2.3
   - Local:        http://localhost:3001
   - Network:      http://0.0.0.0:3001
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 3.9s
 ✓ Compiled /middleware in 443ms (101 modules)
 ○ Compiling /watch/[id] ...
 ✓ Compiled /watch/[id] in 13.2s (2650 modules)
Auth state: { authState: false, userId: undefined }
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 15971ms
 ○ Compiling /api/watch-party ...
 ✓ Compiled /api/content in 8.1s (2718 modules)
 POST /api/watch-party 200 in 10149ms
 POST /api/watch-party 200 in 9359ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 335ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
 POST /api/watch-party 200 in 77ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 11662ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 11780ms
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full     
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 11171ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 387ms
 ○ Compiling /api/related-content ...
 ✓ Compiled /api/related-content in 1746ms (2721 modules)
API: Fetching related content for movie with ID 1233413
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 2050ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
API: Fetching related content for movie with ID 1233413
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 116ms
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full     
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 1813ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 1233413
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 116ms
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 233ms
 ○ Compiling /api/proxy/vidsrc ...
 ✓ Compiled /api/proxy/vidsrc in 1368ms (2723 modules)
Proxying request to: https://vidsrc.xyz/embed/movie?imdb=tt31193180
API: Fetching related content for movie with ID 1233413
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 1057ms
 GET /api/proxy/vidsrc?url=https%3A%2F%2Fvidsrc.xyz%2Fembed%2Fmovie%3Fimdb%3Dtt31193180 200 in 2349ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 806ms (2724 modules)
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /style.css?t=1710289820 404 in 1730ms
 GET /sources.js?t=1745104089 404 in 1740ms
 GET /base64.js?t=1688387834 404 in 1747ms
 GET /reporting.js?t=1688387834 404 in 1740ms
 GET /sbx.js?t=1688387834 404 in 1191ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752434607 404 in 72ms
 ✓ Compiled in 2.6s (2738 modules)
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 585ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 330ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 662ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 1233413
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 336ms
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 338ms
Auth state: { authState: false, userId: undefined }
 GET /style.css?t=1710289820 404 in 1269ms
Auth state: { authState: false, userId: undefined }
API: Fetching related content for movie with ID 1233413
 GET /base64.js?t=1688387834 404 in 288ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 400ms
 GET /sbx.js?t=1688387834 404 in 400ms
 GET /reporting.js?t=1688387834 404 in 404ms
 GET /sources.js?t=1745104089 404 in 409ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752434607 404 in 62ms
 ✓ Compiled in 4.1s (2839 modules)
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 2519ms
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 68ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 293ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 532ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 1233413
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
Auth state: { authState: false, userId: undefined }
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 635ms
 GET /style.css?t=1710289820 404 in 379ms
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 663ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
API: Fetching related content for movie with ID 1233413
 GET /sources.js?t=1745104089 404 in 262ms
 GET /base64.js?t=1688387834 404 in 266ms
 GET /reporting.js?t=1688387834 404 in 262ms
 GET /sbx.js?t=1688387834 404 in 260ms
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 314ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752434607 404 in 73ms
 ✓ Compiled in 1810ms (2738 modules)
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 271ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 120ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 448ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 1233413
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 180ms
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 219ms
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
 GET /style.css?t=1710289820 404 in 420ms
 GET /base64.js?t=1688387834 404 in 426ms
API: Fetching related content for movie with ID 1233413
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
Auth state: { authState: false, userId: undefined }
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 241ms
 GET /sources.js?t=1745104089 404 in 242ms
 GET /reporting.js?t=1688387834 404 in 246ms
 GET /sbx.js?t=1688387834 404 in 244ms
Auth state: { authState: false, userId: undefined }
 GET /f59d610a61063c7ef3ccdc1fd40d2ae6.js?_=1752434607 404 in 58ms
 ✓ Compiled in 1395ms (1425 modules)
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 86ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 140ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 658ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 1233413
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 229ms
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
Redirecting VidSrc resource request: /style.css -> /api/proxy/vidsrc/style.css?t=1710289820
Redirecting VidSrc resource request: /base64.js -> /api/proxy/vidsrc/base64.js?t=1688387834
Redirecting VidSrc resource request: /sources.js -> /api/proxy/vidsrc/sources.js?t=1745104089
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 365ms
Redirecting VidSrc resource request: /reporting.js -> /api/proxy/vidsrc/reporting.js?t=1688387834
Redirecting VidSrc resource request: /sbx.js -> /api/proxy/vidsrc/sbx.js?t=1688387834
 ○ Compiling /api/proxy/vidsrc/[...path] ...
 ✓ Compiled /api/proxy/vidsrc/[...path] in 1679ms (2726 modules)
API: Fetching related content for movie with ID 1233413
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 1782ms
Error: Route "/api/proxy/vidsrc/[...path]" used `params.path`. `params` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis
    at GET (src\app\api\proxy\vidsrc\[...path]\route.ts:20:32)
  18 |   try {
  19 |     const { searchParams } = new URL(request.url);
> 20 |     const pathSegments = params.path || [];
     |                                ^
  21 |     const resourcePath = '/' + pathSegments.join('/');
  22 |     const queryString = searchParams.toString();
  23 |     const fullPath = queryString ? `${resourcePath}?${queryString}` : resourcePath;
Attempting to fetch VidSrc resource from vidsrc.xyz: https://vidsrc.xyz/style.css?t=1710289820
Error: Route "/api/proxy/vidsrc/[...path]" used `params.path`. `params` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis
    at GET (src\app\api\proxy\vidsrc\[...path]\route.ts:20:32)
  18 |   try {
  19 |     const { searchParams } = new URL(request.url);
> 20 |     const pathSegments = params.path || [];
     |                                ^
  21 |     const resourcePath = '/' + pathSegments.join('/');
  22 |     const queryString = searchParams.toString();
  23 |     const fullPath = queryString ? `${resourcePath}?${queryString}` : resourcePath;
Attempting to fetch VidSrc resource from vidsrc.xyz: https://vidsrc.xyz/base64.js?t=1688387834
Error: Route "/api/proxy/vidsrc/[...path]" used `params.path`. `params` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis
    at GET (src\app\api\proxy\vidsrc\[...path]\route.ts:20:32)
  18 |   try {
  19 |     const { searchParams } = new URL(request.url);
> 20 |     const pathSegments = params.path || [];
     |                                ^
  21 |     const resourcePath = '/' + pathSegments.join('/');
  22 |     const queryString = searchParams.toString();
  23 |     const fullPath = queryString ? `${resourcePath}?${queryString}` : resourcePath;
Attempting to fetch VidSrc resource from vidsrc.xyz: https://vidsrc.xyz/reporting.js?t=1688387834
Error: Route "/api/proxy/vidsrc/[...path]" used `params.path`. `params` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis
    at GET (src\app\api\proxy\vidsrc\[...path]\route.ts:20:32)
  18 |   try {
  19 |     const { searchParams } = new URL(request.url);
> 20 |     const pathSegments = params.path || [];
     |                                ^
  21 |     const resourcePath = '/' + pathSegments.join('/');
  22 |     const queryString = searchParams.toString();
  23 |     const fullPath = queryString ? `${resourcePath}?${queryString}` : resourcePath;
Attempting to fetch VidSrc resource from vidsrc.xyz: https://vidsrc.xyz/sources.js?t=1745104089
Error: Route "/api/proxy/vidsrc/[...path]" used `params.path`. `params` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis
    at GET (src\app\api\proxy\vidsrc\[...path]\route.ts:20:32)
  18 |   try {
  19 |     const { searchParams } = new URL(request.url);
> 20 |     const pathSegments = params.path || [];
     |                                ^
  21 |     const resourcePath = '/' + pathSegments.join('/');
  22 |     const queryString = searchParams.toString();
  23 |     const fullPath = queryString ? `${resourcePath}?${queryString}` : resourcePath;
Attempting to fetch VidSrc resource from vidsrc.xyz: https://vidsrc.xyz/sbx.js?t=1688387834
Successfully fetched VidSrc resource from vidsrc.xyz: /sbx.js
 GET /api/proxy/vidsrc/sbx.js?t=1688387834 200 in 4413ms
Successfully fetched VidSrc resource from vidsrc.xyz: /style.css       
 GET /api/proxy/vidsrc/style.css?t=1710289820 200 in 4487ms
Successfully fetched VidSrc resource from vidsrc.xyz: /reporting.js    
 GET /api/proxy/vidsrc/reporting.js?t=1688387834 200 in 4420ms
Successfully fetched VidSrc resource from vidsrc.xyz: /sources.js
 GET /api/proxy/vidsrc/sources.js?t=1745104089 200 in 4435ms
Successfully fetched VidSrc resource from vidsrc.xyz: /base64.js       
 GET /api/proxy/vidsrc/base64.js?t=1688387834 200 in 4440ms
Redirecting VidSrc resource request: /rings.svg -> /api/proxy/vidsrc/rings.svg
Auth state: { authState: false, userId: undefined }
Error: Route "/api/proxy/vidsrc/[...path]" used `params.path`. `params` should be awaited before using its properties. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis
    at GET (src\app\api\proxy\vidsrc\[...path]\route.ts:20:32)
  18 |   try {
  19 |     const { searchParams } = new URL(request.url);
> 20 |     const pathSegments = params.path || [];
     |                                ^
  21 |     const resourcePath = '/' + pathSegments.join('/');
  22 |     const queryString = searchParams.toString();
  23 |     const fullPath = queryString ? `${resourcePath}?${queryString}` : resourcePath;
Attempting to fetch VidSrc resource from vidsrc.xyz: https://vidsrc.xyz/rings.svg
 GET /sbx.html 404 in 529ms
Successfully fetched VidSrc resource from vidsrc.xyz: /rings.svg
 POST /api/watch-party 200 in 176ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 309ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 332ms
 ✓ Compiled in 1836ms (2740 modules)
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 181ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 140ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 420ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 1233413
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Redirecting VidSrc resource request: /style.css -> /api/proxy/vidsrc/style.css?t=1710289820
Redirecting VidSrc resource request: /base64.js -> /api/proxy/vidsrc/base64.js?t=1688387834
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full     
Redirecting VidSrc resource request: /sources.js -> /api/proxy/vidsrc/sources.js?t=1745104089
Redirecting VidSrc resource request: /reporting.js -> /api/proxy/vidsrc/reporting.js?t=1688387834
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 242ms
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 262ms
Redirecting VidSrc resource request: /sbx.js -> /api/proxy/vidsrc/sbx.js?t=1688387834
Redirecting VidSrc resource request: /rings.svg -> /api/proxy/vidsrc/rings.svg
API: Fetching related content for movie with ID 1233413
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 97ms
Attempting to fetch VidSrc resource from vidsrc.xyz: https://vidsrc.xyz/rings.svg
Successfully fetched VidSrc resource from vidsrc.xyz: /rings.svg
 GET /api/proxy/vidsrc/rings.svg 200 in 177ms
Auth state: { authState: false, userId: undefined }
 GET /sbx.html 404 in 396ms
 POST /api/watch-party 200 in 162ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 274ms
Updated existing visitor: 56be20fa-25d2-46f7-8134-56ac6bb373d7 (IP: 127.0.0.1, Device: Desktop)
 POST /api/tracking/visitor 200 in 284ms
 ✓ Compiled in 1630ms (2740 modules)
 GET /watch/1233413?forcePlay=true&contentType=movie 200 in 144ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
 POST /api/watch-party 200 in 342ms
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 803ms
Fetching from TMDB: /3/movie/1233413?language=en-US&append_to_response=videos%2Ccredits%2Csimilar%2Crecommendations
API: Fetching related content for movie with ID 1233413
TMDB API success for /movie/1233413 { resultCount: 'N/A' }
Fetched movie details for ID 1233413: { title: 'Sinners', hasVideos: true, videoCount: 65 }
Enhancing movie content with OMDB data for IMDb ID: tt31193180
Fetching from OMDB: http://www.omdbapi.com/?i=tt31193180&plot=full
API: Found 20 related content items
 GET /api/related-content?id=1233413&type=movie 200 in 271ms
OMDB API success for {"i":"tt31193180","plot":"full"}
API: Fetched movie content: {
  id: '1233413',
  title: 'Sinners',
  imdbId: 'tt31193180',
  tmdbId: '1233413',
  dataSource: 'both'
}
 GET /api/content?id=1233413&type=movie 200 in 330ms
Redirecting VidSrc resource request: /style.css -> /api/proxy/vidsrc/style.css?t=1710289820
Redirecting VidSrc resource request: /base64.js -> /api/proxy/vidsrc/base64.js?t=1688387834
Redirecting VidSrc resource request: /sources.js -> /api/proxy/vidsrc/sources.js?t=1745104089
Redirecting VidSrc resource request: /reporting.js -> /api/proxy/vidsrc/reporting.js?t=1688387834
Redirecting VidSrc resource request: /sbx.js -> /api/proxy/vidsrc/sbx.js?t=1688387834
Redirecting VidSrc resource request: /rings.svg -> /api/proxy/vidsrc/rings.svg
API: Fetching related content for movie with ID 1233413